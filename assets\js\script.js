// Sample product data
const products = [
    {
        id: 1,
        name: "Premium Beef Steak",
        price: 45,
        weight: "500g",
        category: "beef",
        image: "assets/images/beef-steak.jpg",
        description: "Premium quality beef steak, perfect for grilling"
    },
    {
        id: 2,
        name: "Fresh Chicken Breast",
        price: 25,
        weight: "1kg",
        category: "chicken",
        image: "assets/images/chicken-breast.jpg",
        description: "Boneless chicken breast, ideal for healthy meals"
    },
    {
        id: 3,
        name: "Lamb Chops",
        price: 65,
        weight: "500g",
        category: "lamb",
        image: "assets/images/lamb-chops.jpg",
        description: "Tender lamb chops, perfect for special occasions"
    },
    {
        id: 4,
        name: "Ground Beef",
        price: 30,
        weight: "1kg",
        category: "beef",
        image: "assets/images/ground-beef.jpg",
        description: "Fresh ground beef for burgers and cooking"
    },
    {
        id: 5,
        name: "Whole Chicken",
        price: 35,
        weight: "1.5kg",
        category: "chicken",
        image: "assets/images/whole-chicken.jpg",
        description: "Fresh whole chicken, cleaned and ready to cook"
    },
    {
        id: 6,
        name: "<PERSON>tton ",
        price: 55,
        weight: "1kg",
        category: "mutton",
        image: "assets/images/mutton-curry.jpg",
        description: "Perfect cuts for traditional curry dishes"
    },
    {
        id: 7,
        name: "Beef Ribs",
        price: 40,
        weight: "1kg",
        category: "beef",
        image: "assets/images/beef-ribs.jpg",
        description: "Juicy beef ribs for BBQ and grilling"
    },
    {
        id: 8,
        name: "Lamb Leg",
        price: 85,
        weight: "2kg",
        category: "lamb",
        image: "assets/images/lamb-leg.jpg",
        description: "Premium lamb leg for roasting"
    }
];

// Shopping cart
let cart = [];

// DOM elements
const hamburger = document.querySelector('.hamburger');
const navMenu = document.querySelector('.nav-menu');
const productsGrid = document.getElementById('products-grid');
const filterButtons = document.querySelectorAll('.filter-btn');
const cartCount = document.querySelector('.cart-count');

// Initialize the website
document.addEventListener('DOMContentLoaded', function() {
    initializeNavigation();
    displayProducts('all');
    initializeFilters();
    initializeSmoothScrolling();
    updateCartCount();
});

// Navigation functionality
function initializeNavigation() {
    hamburger.addEventListener('click', function() {
        navMenu.classList.toggle('active');
        
        // Animate hamburger
        hamburger.classList.toggle('active');
    });

    // Close mobile menu when clicking on a link
    document.querySelectorAll('.nav-menu a').forEach(link => {
        link.addEventListener('click', () => {
            navMenu.classList.remove('active');
            hamburger.classList.remove('active');
        });
    });
}

// Product display functionality
function displayProducts(category) {
    const filteredProducts = category === 'all' 
        ? products 
        : products.filter(product => product.category === category);

    productsGrid.innerHTML = filteredProducts.map(product => `
        <div class="product-card" data-category="${product.category}">
            <div class="product-image">
                <img src="${product.image}" alt="${product.name}" onerror="this.src='assets/images/placeholder.jpg'">
                <div class="halal-icon">Halal ✓</div>
            </div>
            <div class="product-info">
                <h3 class="product-name">${product.name}</h3>
                <p class="product-weight">${product.weight}</p>
                <div class="product-price">${product.price} AED</div>
                <p class="product-description">${product.description}</p>
                <button class="btn btn-primary" onclick="addToCart(${product.id})">
                    <i class="fas fa-cart-plus"></i> Add to Cart
                </button>
            </div>
        </div>
    `).join('');
}

// Filter functionality
function initializeFilters() {
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            filterButtons.forEach(btn => btn.classList.remove('active'));
            
            // Add active class to clicked button
            this.classList.add('active');
            
            // Filter products
            const category = this.getAttribute('data-filter');
            displayProducts(category);
        });
    });
}

// Cart functionality
function addToCart(productId) {
    const product = products.find(p => p.id === productId);
    const existingItem = cart.find(item => item.id === productId);
    
    if (existingItem) {
        existingItem.quantity += 1;
    } else {
        cart.push({
            ...product,
            quantity: 1
        });
    }
    
    updateCartCount();
    showCartNotification(product.name);
}

function updateCartCount() {
    const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
    cartCount.textContent = totalItems;
}

function showCartNotification(productName) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'cart-notification';
    notification.innerHTML = `
        <i class="fas fa-check-circle"></i>
        <span>${productName} added to cart!</span>
    `;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: #28a745;
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        z-index: 10000;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        animation: slideIn 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // Remove notification after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Smooth scrolling for navigation links
function initializeSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Category card click functionality
document.addEventListener('DOMContentLoaded', function() {
    const categoryCards = document.querySelectorAll('.category-card');
    
    categoryCards.forEach(card => {
        card.addEventListener('click', function() {
            const category = this.getAttribute('data-category');
            
            // Scroll to products section
            document.getElementById('products').scrollIntoView({
                behavior: 'smooth'
            });
            
            // Filter products after a short delay
            setTimeout(() => {
                // Update filter buttons
                filterButtons.forEach(btn => btn.classList.remove('active'));
                const targetButton = document.querySelector(`[data-filter="${category}"]`);
                if (targetButton) {
                    targetButton.classList.add('active');
                }
                
                // Display filtered products
                displayProducts(category);
            }, 500);
        });
    });
});

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    .hamburger.active span:nth-child(1) {
        transform: rotate(-45deg) translate(-5px, 6px);
    }
    
    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }
    
    .hamburger.active span:nth-child(3) {
        transform: rotate(45deg) translate(-5px, -6px);
    }
    
    .product-card {
        transition: all 0.3s ease;
    }
    
    .product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    }
    
    .product-description {
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 1rem;
        line-height: 1.4;
    }
`;

document.head.appendChild(style);

// WhatsApp integration
function openWhatsApp(message = "Hello! I'm interested in your halal meat products.") {
    const phoneNumber = "971XXXXXXXXX"; // Replace with actual WhatsApp number
    const encodedMessage = encodeURIComponent(message);
    const whatsappURL = `https://wa.me/${phoneNumber}?text=${encodedMessage}`;
    window.open(whatsappURL, '_blank');
}

// Add WhatsApp functionality to buttons
document.addEventListener('DOMContentLoaded', function() {
    const whatsappButtons = document.querySelectorAll('.whatsapp-btn');
    whatsappButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            openWhatsApp();
        });
    });
});

// Intersection Observer for animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver(function(entries) {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
        }
    });
}, observerOptions);

// Observe elements for animation
document.addEventListener('DOMContentLoaded', function() {
    const animateElements = document.querySelectorAll('.category-card, .product-card, .review-card, .offer-card');
    
    animateElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
});

// Simple cart modal (basic implementation)
function showCart() {
    if (cart.length === 0) {
        alert('Your cart is empty!');
        return;
    }
    
    let cartHTML = 'Your Cart:\n\n';
    let total = 0;
    
    cart.forEach(item => {
        const itemTotal = item.price * item.quantity;
        cartHTML += `${item.name} (${item.weight}) x${item.quantity} = ${itemTotal} AED\n`;
        total += itemTotal;
    });
    
    cartHTML += `\nTotal: ${total} AED\n\nWould you like to order via WhatsApp?`;
    
    if (confirm(cartHTML)) {
        const orderMessage = `Hello! I'd like to order:\n\n${cart.map(item => 
            `${item.name} (${item.weight}) x${item.quantity} = ${item.price * item.quantity} AED`
        ).join('\n')}\n\nTotal: ${total} AED\n\nPlease confirm availability and delivery details.`;
        
        openWhatsApp(orderMessage);
    }
}

// Add cart click functionality
document.addEventListener('DOMContentLoaded', function() {
    const cartIcon = document.querySelector('.cart-icon');
    if (cartIcon) {
        cartIcon.addEventListener('click', function(e) {
            e.preventDefault();
            showCart();
        });
    }
});
