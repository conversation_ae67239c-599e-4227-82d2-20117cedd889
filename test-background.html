<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Background Test</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            height: 100vh;
            background-image: url('assets/images/shop.jpg');
            background-attachment: fixed;
            background-position: center center;
            background-repeat: no-repeat;
            background-size: cover;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: Arial, sans-serif;
        }
        
        .test-content {
            background: rgba(255, 255, 255, 0.9);
            padding: 2rem;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .test-image {
            max-width: 300px;
            border: 2px solid #e60000;
            border-radius: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="test-content">
        <h1>Background Image Test</h1>
        <p>If you can see this content clearly, the background is working!</p>
        
        <h3>Direct Image Test:</h3>
        <img src="assets/images/shop.jpg" alt="Shop Image" class="test-image" 
             onload="this.style.border='2px solid green'; this.nextElementSibling.innerHTML='✅ Image loaded successfully!'" 
             onerror="this.style.border='2px solid red'; this.nextElementSibling.innerHTML='❌ Image failed to load'">
        <p id="image-status">Loading...</p>
        
        <h3>Background Status:</h3>
        <p id="bg-status">Checking background...</p>
    </div>
    
    <script>
        // Check if background image is applied
        const bgImage = window.getComputedStyle(document.body).backgroundImage;
        const bgStatus = document.getElementById('bg-status');
        
        if (bgImage && bgImage !== 'none') {
            bgStatus.innerHTML = '✅ Background CSS applied: ' + bgImage;
            bgStatus.style.color = 'green';
        } else {
            bgStatus.innerHTML = '❌ Background CSS not applied';
            bgStatus.style.color = 'red';
        }
        
        // Add some scrollable content to test fixed background
        document.body.innerHTML += `
            <div style="position: absolute; top: 100vh; background: rgba(255,255,255,0.9); padding: 2rem; margin: 2rem; border-radius: 10px;">
                <h2>Scroll Test</h2>
                <p>Scroll up and down to see if the background stays fixed!</p>
                <div style="height: 500px; background: linear-gradient(45deg, #f0f0f0, #e0e0e0); margin: 20px 0; border-radius: 10px; display: flex; align-items: center; justify-content: center;">
                    <p>More content to test scrolling...</p>
                </div>
            </div>
        `;
    </script>
</body>
</html>
