#!/usr/bin/env python3
"""
Simple HTTP server for testing the Qayyum Meat House website locally.
Run this file to start a local development server.
"""

import http.server
import socketserver
import webbrowser
import os
import sys

# Configuration
PORT = 8000
DIRECTORY = "."

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=DIRECTORY, **kwargs)
    
    def end_headers(self):
        # Add CORS headers for local development
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

def start_server():
    """Start the local development server."""
    global PORT

    try:
        with socketserver.TCPServer(("", PORT), CustomHTTPRequestHandler) as httpd:
            print(f"🚀 Qayyum Meat House Website Server")
            print(f"📍 Serving at: http://localhost:{PORT}")
            print(f"📁 Directory: {os.path.abspath(DIRECTORY)}")
            print(f"🌐 Opening browser...")
            print(f"⏹️  Press Ctrl+C to stop the server")
            print("-" * 50)

            # Open browser automatically
            webbrowser.open(f'http://localhost:{PORT}')

            # Start serving
            httpd.serve_forever()

    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
        sys.exit(0)
    except OSError as e:
        if e.errno == 48 or "address already in use" in str(e).lower():  # Address already in use
            print(f"❌ Port {PORT} is already in use.")
            print(f"💡 Try a different port or stop the existing server.")

            # Try alternative ports
            for alt_port in [8001, 8080, 3000, 5000]:
                try:
                    PORT = alt_port
                    with socketserver.TCPServer(("", PORT), CustomHTTPRequestHandler) as httpd:
                        print(f"✅ Using alternative port: {PORT}")
                        print(f"🌐 Opening browser at: http://localhost:{PORT}")
                        webbrowser.open(f'http://localhost:{PORT}')
                        httpd.serve_forever()
                        break
                except OSError:
                    continue
            else:
                print("❌ No available ports found. Please try again later.")
                sys.exit(1)
        else:
            print(f"❌ Server error: {e}")
            sys.exit(1)

if __name__ == "__main__":
    print("🥩 Qayyum Meat House - Local Development Server")
    print("=" * 50)
    
    # Check if index.html exists
    if not os.path.exists("index.html"):
        print("❌ index.html not found in current directory")
        print("💡 Make sure you're running this from the website root directory")
        sys.exit(1)
    
    start_server()
