<!DOCTYPE html>
<html>
<head>
    <title>Create Placeholder Image</title>
</head>
<body>
    <canvas id="canvas" width="400" height="300"></canvas>
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // Create gradient background
        const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
        gradient.addColorStop(0, '#e60000');
        gradient.addColorStop(1, '#cc0000');
        
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        // Add text
        ctx.fillStyle = 'white';
        ctx.font = 'bold 24px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('Qayyum Meat House', canvas.width / 2, canvas.height / 2 - 20);
        
        ctx.font = 'bold 16px Arial';
        ctx.fillText('✓ HALAL CERTIFIED', canvas.width / 2, canvas.height / 2 + 20);
        
        // Convert to image and download
        const link = document.createElement('a');
        link.download = 'placeholder.jpg';
        link.href = canvas.toDataURL('image/jpeg', 0.8);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    </script>
</body>
</html>
