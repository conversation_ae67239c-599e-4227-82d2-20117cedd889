// Offal Product Gallery System
document.addEventListener('DOMContentLoaded', function() {
    // Product image galleries - you can add more images for each product
    const productGalleries = {
        liver: [
            { src: 'assets/images/offal-section/liver/liver1.jpg', alt: 'Fresh Liver - Main View' },
            { src: 'assets/images/offal-section/liver/liver1.jpg', alt: 'Fresh Liver - Close Up' },
            { src: 'assets/images/offal-section/liver/liver1.jpg', alt: 'Fresh Liver - Cut View' },
            { src: 'assets/images/offal-section/liver/liver1.jpg', alt: 'Fresh Liver - Prepared' }
        ],
        kidney: [
            { src: 'assets/images/offal-section/kidney/kidney.jpg', alt: 'Fresh Kidney - Main View' },
            { src: 'assets/images/offal-section/kidney/kidney2.jpg', alt: 'Fresh Kidney - Close Up' },
            { src: 'assets/images/offal-section/kidney/kidney3.jpg', alt: 'Fresh Kidney - Cut View' },
            { src: 'assets/images/offal-section/kidney/kidney.jpg', alt: 'Fresh Kidney - Prepared' }
        ],
        head: [
            { src: 'assets/images/offal-section/head/head1.jpg', alt: 'Fresh Head - Main View' },
            { src: 'assets/images/offal-section/head/head2.jpg', alt: 'Fresh Head - Side View' },
            { src: 'assets/images/head1.jpg', alt: 'Fresh Head - Prepared' },
            { src: 'assets/images/head1.jpg', alt: 'Fresh Head - Traditional Style' }
        ],
        fat: [
            { src: 'assets/images/fat.jpg', alt: 'Fresh Fat - Main View' },
            { src: 'assets/images/fat.jpg', alt: 'Fresh Fat - Cut View' },
            { src: 'assets/images/fat.jpg', alt: 'Fresh Fat - Quality Check' },
            { src: 'assets/images/fat.jpg', alt: 'Fresh Fat - Prepared' }
        ],
        tongue: [
            { src: 'assets/images/offal-section/tongue/tongue1.jpg', alt: 'Fresh Tongue - Main View' },
            { src: 'assets/images/offal-section/tongue/tongue2.jpg', alt: 'Fresh Tongue - Close Up' },
            { src: 'assets/images/tongue1.jpg', alt: 'Fresh Tongue - Cut View' },
            { src: 'assets/images/tongue1.jpg', alt: 'Fresh Tongue - Cooked' }
        ],
        brain: [
            { src: 'assets/images/brain.jpg', alt: 'Fresh Brain - Main View' },
            { src: 'assets/images/brain.jpg', alt: 'Fresh Brain - Close Up' },
            { src: 'assets/images/brain.jpg', alt: 'Fresh Brain - Prepared' },
            { src: 'assets/images/brain.jpg', alt: 'Fresh Brain - Traditional Style' }
        ],
        feet: [
            { src: 'assets/images/feet.jpg', alt: 'Fresh Paya - Main View' },
            { src: 'assets/images/feet.jpg', alt: 'Fresh Paya - Close Up' },
            { src: 'assets/images/feet.jpg', alt: 'Fresh Paya - Cleaned' },
            { src: 'assets/images/feet.jpg', alt: 'Fresh Paya - Cooked' }
        ],
        mixed: [
            { src: 'assets/images/mix.jpg', alt: 'Mixed Offal - Main View' },
            { src: 'assets/images/mix.jpg', alt: 'Mixed Offal - Variety Pack' },
            { src: 'assets/images/mix.jpg', alt: 'Mixed Offal - Fresh Selection' },
            { src: 'assets/images/mix.jpg', alt: 'Mixed Offal - Traditional Mix' }
        ]
    };

    // Product names for modal titles
    const productNames = {
        liver: 'Fresh Liver Gallery',
        kidney: 'Fresh Kidney Gallery',
        head: 'Fresh Head Gallery',
        fat: 'Fresh Fat Gallery',
        tongue: 'Fresh Tongue Gallery',
        brain: 'Fresh Brain Gallery',
        feet: 'Fresh Paya Gallery',
        mixed: 'Mixed Offal Pack Gallery'
    };

    // Modal elements
    const modal = document.getElementById('productModal');
    const modalTitle = document.getElementById('modalTitle');
    const mainImage = document.getElementById('mainImage');
    const currentImageSpan = document.getElementById('currentImage');
    const totalImagesSpan = document.getElementById('totalImages');
    const thumbnailGrid = document.getElementById('thumbnailGrid');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const closeBtn = document.querySelector('.close');

    let currentProduct = '';
    let currentImageIndex = 0;
    let currentGallery = [];

    // Add click event listeners to all product cards
    const productCards = document.querySelectorAll('.clickable-product');
    productCards.forEach(card => {
        card.addEventListener('click', function() {
            const productType = this.getAttribute('data-product');
            openGallery(productType);
        });
    });

    // Open gallery function
    function openGallery(productType) {
        if (!productGalleries[productType]) return;

        currentProduct = productType;
        currentGallery = productGalleries[productType];
        currentImageIndex = 0;

        // Set modal title
        modalTitle.textContent = productNames[productType] || 'Product Gallery';

        // Set total images
        totalImagesSpan.textContent = currentGallery.length;

        // Create thumbnails
        createThumbnails();

        // Show first image
        showImage(0);

        // Show modal
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden'; // Prevent background scrolling
    }

    // Create thumbnail images
    function createThumbnails() {
        thumbnailGrid.innerHTML = '';
        
        currentGallery.forEach((image, index) => {
            const thumbnail = document.createElement('img');
            thumbnail.src = image.src;
            thumbnail.alt = image.alt;
            thumbnail.className = 'thumbnail';
            thumbnail.addEventListener('click', () => showImage(index));
            
            if (index === 0) {
                thumbnail.classList.add('active');
            }
            
            thumbnailGrid.appendChild(thumbnail);
        });
    }

    // Show specific image
    function showImage(index) {
        if (index < 0 || index >= currentGallery.length) return;

        currentImageIndex = index;
        const image = currentGallery[index];

        // Update main image
        mainImage.src = image.src;
        mainImage.alt = image.alt;

        // Update counter
        currentImageSpan.textContent = index + 1;

        // Update active thumbnail
        const thumbnails = thumbnailGrid.querySelectorAll('.thumbnail');
        thumbnails.forEach((thumb, i) => {
            thumb.classList.toggle('active', i === index);
        });

        // Update navigation buttons
        prevBtn.disabled = index === 0;
        nextBtn.disabled = index === currentGallery.length - 1;
    }

    // Navigation event listeners
    prevBtn.addEventListener('click', () => {
        if (currentImageIndex > 0) {
            showImage(currentImageIndex - 1);
        }
    });

    nextBtn.addEventListener('click', () => {
        if (currentImageIndex < currentGallery.length - 1) {
            showImage(currentImageIndex + 1);
        }
    });

    // Close modal function
    function closeModal() {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto'; // Restore background scrolling
    }

    // Close modal event listeners
    closeBtn.addEventListener('click', closeModal);

    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal();
        }
    });

    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (modal.style.display === 'block') {
            switch(e.key) {
                case 'Escape':
                    closeModal();
                    break;
                case 'ArrowLeft':
                    if (currentImageIndex > 0) {
                        showImage(currentImageIndex - 1);
                    }
                    break;
                case 'ArrowRight':
                    if (currentImageIndex < currentGallery.length - 1) {
                        showImage(currentImageIndex + 1);
                    }
                    break;
            }
        }
    });

    // Touch/swipe support for mobile
    let touchStartX = 0;
    let touchEndX = 0;

    mainImage.addEventListener('touchstart', function(e) {
        touchStartX = e.changedTouches[0].screenX;
    });

    mainImage.addEventListener('touchend', function(e) {
        touchEndX = e.changedTouches[0].screenX;
        handleSwipe();
    });

    function handleSwipe() {
        const swipeThreshold = 50;
        const diff = touchStartX - touchEndX;

        if (Math.abs(diff) > swipeThreshold) {
            if (diff > 0 && currentImageIndex < currentGallery.length - 1) {
                // Swipe left - next image
                showImage(currentImageIndex + 1);
            } else if (diff < 0 && currentImageIndex > 0) {
                // Swipe right - previous image
                showImage(currentImageIndex - 1);
            }
        }
    }
});
