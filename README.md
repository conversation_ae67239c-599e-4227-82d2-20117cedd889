# Qayyum Meat House Website

A modern, responsive website for Qayyum Meat House - Dubai's premium halal meat supplier from Waterfront Market.

## Features

### 🎯 Core Features
- **Responsive Design**: Mobile-first approach with clean, modern UI
- **Product Catalog**: Organized categories (<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>tton)
- **Product Filtering**: Filter products by category
- **Special Offers**: Promotional banners and discount displays
- **Customer Reviews**: Social proof with star ratings
- **Contact Integration**: WhatsApp ordering and contact forms

### 🎨 Design Elements
- **Brand Colors**: Red (#e60000), Black, White, Gold accents
- **Typography**: Poppins font family for modern readability
- **Icons**: Font Awesome icons for consistent UI
- **Animations**: Smooth transitions and hover effects
- **Trust Badges**: Halal certification and quality indicators

### 📱 Responsive Features
- Mobile hamburger navigation
- Flexible grid layouts
- Touch-friendly buttons
- Optimized images and content

## File Structure

```
website/
├── index.html              # Main homepage
├── assets/
│   ├── css/
│   │   └── style.css      # Main stylesheet
│   ├── js/
│   │   └── script.js      # Interactive functionality
│   └── images/            # Product and hero images
├── pages/                 # Additional pages (future expansion)
└── README.md             # This file
```

## Setup Instructions

1. **Clone or Download** the project files
2. **Add Images**: Place product images in `assets/images/` folder:
   - `hero-meat.jpg` - Hero section background
   - `about-shop.jpg` - About section image
   - Product images: `beef-steak.jpg`, `chicken-breast.jpg`, etc.
   - `placeholder.jpg` - Fallback image

3. **Update Contact Information**:
   - Replace `+971 XX XXX XXXX` with actual phone number
   - Update `971XXXXXXXXX` in JavaScript for WhatsApp integration
   - Add real email address and social media links

4. **Customize Content**:
   - Update product information in `assets/js/script.js`
   - Modify pricing and descriptions as needed
   - Add real customer reviews

## Product Management

Products are managed in the JavaScript file (`assets/js/script.js`). Each product has:
- ID, name, price, weight
- Category (beef, chicken, lamb, mutton)
- Image path and description

To add new products, extend the `products` array with the same structure.

## WhatsApp Integration

The website includes WhatsApp ordering functionality:
- Cart items are formatted into WhatsApp messages
- Direct "Chat to Order" buttons
- Automatic message formatting with product details

**Setup**: Update the phone number in the JavaScript file:
```javascript
const phoneNumber = "971XXXXXXXXX"; // Replace with actual number
```

## Customization

### Colors
Update CSS variables in `assets/css/style.css`:
```css
:root {
    --primary-red: #e60000;
    --dark-red: #cc0000;
    --black: #1a1a1a;
    --white: #ffffff;
    --light-gray: #f8f9fa;
    --gray: #6c757d;
    --gold: #ffd700;
}
```

### Content
- **Hero Section**: Update slogans and badges in `index.html`
- **About Section**: Modify Muhammad Qayyum's story
- **Products**: Update the products array in JavaScript
- **Reviews**: Replace with real customer testimonials

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers (iOS Safari, Chrome Mobile)

## Performance Features

- Optimized CSS with minimal dependencies
- Lazy loading for images
- Efficient JavaScript with event delegation
- Mobile-first responsive design
- Font optimization with Google Fonts

## Future Enhancements

- Online payment integration
- User accounts and order history
- Inventory management system
- Advanced product search
- Multi-language support (Arabic/English)
- Delivery tracking system

## Support

For technical support or customization requests, contact the development team.

---

**Qayyum Meat House** - Pure. Halal. Fresh - From Our Market to Your Plate.
