// Mutton Product Gallery System
document.addEventListener('DOMContentLoaded', function() {
    // Product image galleries - you can add more images for each product
    const productGalleries = {
        'mutton-shoulder': [
            { src: 'assets/images/mutton-shoulder.jpg', alt: '<PERSON><PERSON> Shoulder - Main View' },
            { src: 'assets/images/mutton-shoulder.jpg', alt: 'Mutton Shoulder - Cut View' },
            { src: 'assets/images/mutton-shoulder.jpg', alt: 'Mutton Shoulder - Quality Check' },
            { src: 'assets/images/mutton-shoulder.jpg', alt: 'Mutton Shoulder - Prepared' }
        ],
        'mutton-leg': [
            { src: 'assets/images/mutton-leg.jpg', alt: 'Mutton Leg - Main View' },
            { src: 'assets/images/mutton-leg.jpg', alt: 'Mutton Leg - Whole Cut' },
            { src: 'assets/images/mutton-leg.jpg', alt: 'Mutton Leg - Premium Quality' },
            { src: 'assets/images/mutton-leg.jpg', alt: 'Mutton Leg - Family Size' }
        ],
        'mutton-chops': [
            { src: 'assets/images/mutton-chops.jpg', alt: 'Mutton Chops - Main View' },
            { src: 'assets/images/mutton-chops.jpg', alt: 'Mutton Chops - Grill Ready' },
            { src: 'assets/images/mutton-chops.jpg', alt: 'Mutton Chops - Perfect Cut' },
            { src: 'assets/images/mutton-chops.jpg', alt: 'Mutton Chops - Restaurant Quality' }
        ],
        'mutton-ribs': [
            { src: 'assets/images/mutton-ribs.jpg', alt: 'Mutton Ribs - Main View' },
            { src: 'assets/images/mutton-ribs.jpg', alt: 'Mutton Ribs - BBQ Ready' },
            { src: 'assets/images/mutton-ribs.jpg', alt: 'Mutton Ribs - Juicy Cut' },
            { src: 'assets/images/mutton-ribs.jpg', alt: 'Mutton Ribs - Full Rack' }
        ],
        'ground-mutton': [
            { src: 'assets/images/ground-mutton.jpg', alt: 'Ground Mutton - Main View' },
            { src: 'assets/images/ground-mutton.jpg', alt: 'Ground Mutton - Fresh Ground' },
            { src: 'assets/images/ground-mutton.jpg', alt: 'Ground Mutton - Keema Ready' },
            { src: 'assets/images/ground-mutton.jpg', alt: 'Ground Mutton - Versatile Use' }
        ],
        'mutton-stew': [
            { src: 'assets/images/mutton-stew.jpg', alt: 'Mutton Stew Pieces - Main View' },
            { src: 'assets/images/mutton-stew.jpg', alt: 'Mutton Stew Pieces - With Bone' },
            { src: 'assets/images/mutton-stew.jpg', alt: 'Mutton Stew Pieces - Perfect Size' },
            { src: 'assets/images/mutton-stew.jpg', alt: 'Mutton Stew Pieces - Curry Ready' }
        ]
    };

    // Product names for modal titles
    const productNames = {
        'mutton-shoulder': 'Mutton Shoulder Gallery',
        'mutton-leg': 'Mutton Leg Gallery',
        'mutton-chops': 'Mutton Chops Gallery',
        'mutton-ribs': 'Mutton Ribs Gallery',
        'ground-mutton': 'Ground Mutton Gallery',
        'mutton-stew': 'Mutton Stew Pieces Gallery'
    };

    // Modal elements
    const modal = document.getElementById('productModal');
    const modalTitle = document.getElementById('modalTitle');
    const mainImage = document.getElementById('mainImage');
    const currentImageSpan = document.getElementById('currentImage');
    const totalImagesSpan = document.getElementById('totalImages');
    const thumbnailGrid = document.getElementById('thumbnailGrid');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const closeBtn = document.querySelector('.close');

    let currentProduct = '';
    let currentImageIndex = 0;
    let currentGallery = [];

    // Add click event listeners to all product cards
    const productCards = document.querySelectorAll('.clickable-product');
    productCards.forEach(card => {
        card.addEventListener('click', function() {
            const productType = this.getAttribute('data-product');
            openGallery(productType);
        });
    });

    // Open gallery function
    function openGallery(productType) {
        if (!productGalleries[productType]) return;

        currentProduct = productType;
        currentGallery = productGalleries[productType];
        currentImageIndex = 0;

        // Set modal title
        modalTitle.textContent = productNames[productType] || 'Product Gallery';

        // Set total images
        totalImagesSpan.textContent = currentGallery.length;

        // Create thumbnails
        createThumbnails();

        // Show first image
        showImage(0);

        // Show modal
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden'; // Prevent background scrolling
    }

    // Create thumbnail images
    function createThumbnails() {
        thumbnailGrid.innerHTML = '';
        
        currentGallery.forEach((image, index) => {
            const thumbnail = document.createElement('img');
            thumbnail.src = image.src;
            thumbnail.alt = image.alt;
            thumbnail.className = 'thumbnail';
            thumbnail.addEventListener('click', () => showImage(index));
            
            if (index === 0) {
                thumbnail.classList.add('active');
            }
            
            thumbnailGrid.appendChild(thumbnail);
        });
    }

    // Show specific image
    function showImage(index) {
        if (index < 0 || index >= currentGallery.length) return;

        currentImageIndex = index;
        const image = currentGallery[index];

        // Update main image
        mainImage.src = image.src;
        mainImage.alt = image.alt;

        // Update counter
        currentImageSpan.textContent = index + 1;

        // Update active thumbnail
        const thumbnails = thumbnailGrid.querySelectorAll('.thumbnail');
        thumbnails.forEach((thumb, i) => {
            thumb.classList.toggle('active', i === index);
        });

        // Update navigation buttons
        prevBtn.disabled = index === 0;
        nextBtn.disabled = index === currentGallery.length - 1;
    }

    // Navigation event listeners
    prevBtn.addEventListener('click', () => {
        if (currentImageIndex > 0) {
            showImage(currentImageIndex - 1);
        }
    });

    nextBtn.addEventListener('click', () => {
        if (currentImageIndex < currentGallery.length - 1) {
            showImage(currentImageIndex + 1);
        }
    });

    // Close modal function
    function closeModal() {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto'; // Restore background scrolling
    }

    // Close modal event listeners
    closeBtn.addEventListener('click', closeModal);

    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal();
        }
    });

    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (modal.style.display === 'block') {
            switch(e.key) {
                case 'Escape':
                    closeModal();
                    break;
                case 'ArrowLeft':
                    if (currentImageIndex > 0) {
                        showImage(currentImageIndex - 1);
                    }
                    break;
                case 'ArrowRight':
                    if (currentImageIndex < currentGallery.length - 1) {
                        showImage(currentImageIndex + 1);
                    }
                    break;
            }
        }
    });

    // Touch/swipe support for mobile
    let touchStartX = 0;
    let touchEndX = 0;

    mainImage.addEventListener('touchstart', function(e) {
        touchStartX = e.changedTouches[0].screenX;
    });

    mainImage.addEventListener('touchend', function(e) {
        touchEndX = e.changedTouches[0].screenX;
        handleSwipe();
    });

    function handleSwipe() {
        const swipeThreshold = 50;
        const diff = touchStartX - touchEndX;

        if (Math.abs(diff) > swipeThreshold) {
            if (diff > 0 && currentImageIndex < currentGallery.length - 1) {
                // Swipe left - next image
                showImage(currentImageIndex + 1);
            } else if (diff < 0 && currentImageIndex > 0) {
                // Swipe right - previous image
                showImage(currentImageIndex - 1);
            }
        }
    }
});
