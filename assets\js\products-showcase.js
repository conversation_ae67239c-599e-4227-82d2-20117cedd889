// Qayyum Meat House - Product Showcase (No Pricing)
const products = [
    // LAMB PRODUCTS
    {
        id: 1,
        name: "Australian Lamb & Mutton",
        weight: "Available in various cuts",
        category: "lamb",
        image: "assets/images/australian-lamb.jpg",
        description: "Premium Australian meat, air-flown for freshness. Perfect for roasting and grilling.",
        origin: "Australia",
        airFlown: true
    },
    {
        id: 2,
        name: "Kenya Lamb",
        weight: "Available in various cuts",
        category: "lamb",
        image: "assets/images/kinea-lamb.jpg",
        description: "Fresh Kenya lamb, air-transported for optimal quality. Ideal for traditional dishes.",
        origin: "Kenya",
        airFlown: true
    },
    {
        id: 3,
        name: "South African Lamb",
        weight: "Available in various cuts",
        category: "lamb",
        image: "assets/images/african-lamb.jpg",
        description: "High-quality African lamb, air-flown daily. Excellent for curries and roasts.",
        origin: "Africa",
        airFlown: true
    },
    
    // GOAT PRODUCTS
    {
        id: 4,
        name: "Kenya Goat",
        weight: "Available in various cuts",
        category: "mutton",
        image: "assets/images/kinea-goat.jpg",
        description: "Fresh Kenya goat meat, air-transported for premium quality. Perfect for traditional cooking.",
        origin: "Kenya",
        airFlown: true
    },
    
    // BEEF PRODUCTS
    {
        id: 5,
        name: "Pakistani Beef",
        weight: "Available with/without bone",
        category: "beef",
        image: "assets/images/pakistani-beef-bone.jpg",
        description: "Authentic Pakistani beef available with or without bone. Rich flavor, perfect for traditional curries, stews, and steaks.",
        origin: "Pakistan",
        airFlown: true
    },

    // OFFAL PRODUCTS
    {
        id: 6,
        name: "Fresh Offal",
        weight: "Available per kg",
        category: "offal",
        image: "assets/images/offal.jpg",
        description: "Fresh offal including liver, kidney, heart, and other organ meats. Traditional delicacies for authentic cooking.",
        origin: "Local/Fresh"
    },
    
    // CHICKEN PRODUCTS
    {
        id: 7,
        name: "Chicken - Whole",
        weight: "1.5kg average",
        category: "chicken",
        image: "assets/images/chicken-whole.jpg",
        description: "Fresh whole chicken, cleaned and ready to cook. Perfect for roasting or cutting into pieces."
    },
    {
        id: 8,
        name: "Chicken Breast",
        weight: "Available per kg",
        category: "chicken",
        image: "assets/images/chicken-breast.jpg",
        description: "Boneless chicken breast pieces. Lean protein, ideal for healthy meals and grilling."
    },
    {
        id: 9,
        name: "Chicken Thighs",
        weight: "Available per kg",
        category: "chicken",
        image: "assets/images/chicken-thighs.jpg",
        description: "Juicy chicken thigh pieces. Perfect for curries, BBQ, and traditional cooking."
    },
    {
        id: 10,
        name: "Chicken Wings",
        weight: "Available per kg",
        category: "chicken",
        image: "assets/images/chicken-wings.jpg",
        description: "Fresh chicken wings. Great for appetizers, BBQ, and party platters."
    },
    {
        id: 11,
        name: "Chicken Drumsticks",
        weight: "Available per kg",
        category: "chicken",
        image: "assets/images/chicken-drumsticks.jpg",
        description: "Tender chicken drumsticks. Kids' favorite, perfect for grilling and frying."
    },
    {
        id: 12,
        name: "Chicken Mixed Pieces",
        weight: "Available per kg",
        category: "chicken",
        image: "assets/images/chicken-mixed.jpg",
        description: "Assorted chicken pieces - thighs, drumsticks, and wings. Great value family pack."
    }
];

// DOM elements
const hamburger = document.querySelector('.hamburger');
const navMenu = document.querySelector('.nav-menu');
const productsGrid = document.getElementById('products-grid');
const filterButtons = document.querySelectorAll('.filter-btn');

// Initialize the website
document.addEventListener('DOMContentLoaded', function() {
    initializeNavigation();
    if (productsGrid) {
        displayProducts('all');
        initializeFilters();
    }
    initializeSmoothScrolling();
});

// Navigation functionality
function initializeNavigation() {
    if (hamburger && navMenu) {
        hamburger.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            hamburger.classList.toggle('active');
        });

        // Close mobile menu when clicking on a link
        document.querySelectorAll('.nav-menu a').forEach(link => {
            link.addEventListener('click', () => {
                navMenu.classList.remove('active');
                hamburger.classList.remove('active');
            });
        });
    }
}

// Product display functionality 
function displayProducts(category) {
    if (!productsGrid) return;
    
    const filteredProducts = category === 'all' 
        ? products 
        : products.filter(product => product.category === category);

    productsGrid.innerHTML = filteredProducts.map(product => {
        const clickableCategories = ['chicken', 'lamb', 'beef', 'mutton', 'offal'];
        const isClickable = clickableCategories.includes(product.category);
        const cardClass = isClickable ? 'product-card clickable-card' : 'product-card';

        let clickHandler = '';
        let viewMoreButton = '';
        let clickHint = '';

        if (isClickable) {
            const categoryPages = {
                'chicken': 'chicken-products.html',
                'lamb': 'lamb-products.html',
                'beef': 'beef-products.html',
                'mutton': 'mutton-products.html',
                'offal': 'offal-products.html'
            };

            const categoryLabels = {
                'chicken': 'Chicken',
                'lamb': 'Lamb',
                'beef': 'Beef',
                'mutton': 'Mutton',
                'offal': 'Offal'
            };

            clickHandler = `onclick="window.location.href='${categoryPages[product.category]}'"`;
            viewMoreButton = `<div class="view-more-btn">🔍 View All ${categoryLabels[product.category]} Products</div>`;
            clickHint = `<p class="click-hint">👆 Click to see all ${categoryLabels[product.category].toLowerCase()} varieties</p>`;
        }

        return `
        <div class="${cardClass}" data-category="${product.category}" ${clickHandler}>
            <div class="product-image">
                <img src="${product.image}" alt="${product.name}" onerror="this.src='assets/images/placeholder.jpg'">
                <div class="halal-icon">Halal ✓</div>
                ${product.airFlown ? '<div class="air-flown-badge">✈️ Air Flown</div>' : ''}
                ${viewMoreButton}
            </div>
            <div class="product-info">
                <h3 class="product-name">${product.name}</h3>
                ${product.origin ? `<p class="product-origin">Origin: ${product.origin}</p>` : ''}
                <p class="product-weight">${product.weight}</p>
                ${product.boneIn !== undefined ? `<p class="bone-info">${product.boneIn ? 'With Bone' : 'Boneless'}</p>` : ''}
                <p class="product-description">${product.description}</p>
                ${clickHint}
            </div>
        </div>
        `;
    }).join('');
}

// Filter functionality
function initializeFilters() {
    if (!filterButtons.length) return;
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            filterButtons.forEach(btn => btn.classList.remove('active'));
            
            // Add active class to clicked button
            this.classList.add('active');
            
            // Filter products
            const category = this.getAttribute('data-filter');
            displayProducts(category);
        });
    });
}

// Smooth scrolling for navigation links
function initializeSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Category card click functionality (for homepage)
document.addEventListener('DOMContentLoaded', function() {
    const categoryCards = document.querySelectorAll('.category-card');
    
    categoryCards.forEach(card => {
        card.addEventListener('click', function() {
            const category = this.getAttribute('data-category');
            
            // If we're on the homepage, scroll to products section
            const productsSection = document.getElementById('products');
            if (productsSection) {
                productsSection.scrollIntoView({
                    behavior: 'smooth'
                });
                
                // Filter products after a short delay
                setTimeout(() => {
                    // Update filter buttons
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    const targetButton = document.querySelector(`[data-filter="${category}"]`);
                    if (targetButton) {
                        targetButton.classList.add('active');
                    }
                    
                    // Display filtered products
                    displayProducts(category);
                }, 500);
            }
        });
    });
});

// WhatsApp integration
function openWhatsApp(message = "Hello! I'm interested in your halal meat products. Could you please provide more information about availability and pricing?") {
    const phoneNumber = "971XXXXXXXXX"; // Replace with actual WhatsApp number
    const encodedMessage = encodeURIComponent(message);
    const whatsappURL = `https://wa.me/${phoneNumber}?text=${encodedMessage}`;
    window.open(whatsappURL, '_blank');
}

// Add WhatsApp functionality to buttons
document.addEventListener('DOMContentLoaded', function() {
    const whatsappButtons = document.querySelectorAll('.whatsapp-btn');
    whatsappButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            openWhatsApp();
        });
    });
});

// Intersection Observer for animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver(function(entries) {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
        }
    });
}, observerOptions);

// Observe elements for animation
document.addEventListener('DOMContentLoaded', function() {
    const animateElements = document.querySelectorAll('.category-card, .product-card, .review-card, .offer-card');
    
    animateElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
});
