/*// <PERSON><PERSON>yum Meat House - Product Showcase (No Pricing)
const products = [
    // LAMB PRODUCTS - Representative Product
    {
        id: 1,
        name: "Premium Lamb Collection",
        weight: "Available in various cuts",
        category: "lamb",
        image: "assets/images/australian-lamb.jpg",
        description: "Premium lamb varieties including Australian, Kenya, and South African lamb - all air-flown for freshness.",
        origin: "Multiple Origins",
        airFlown: true,
        detailPage: "lamb-products.html"
    },

    // GOAT PRODUCTS - Representative Product
    {
        id: 2,
        name: "Fresh Goat Meat",
        weight: "Available in various cuts",
        category: "mutton",
        image: "assets/images/kinea-goat.jpg",
        description: "Fresh Kenya goat meat, air-transported for premium quality. Perfect for traditional cooking.",
        origin: "Kenya",
        airFlown: true,
        detailPage: "mutton-products.html"
    },

    // BEEF PRODUCTS - Representative Product
    {
        id: 3,
        name: "Pakistani Beef Collection",
        weight: "Available with/without bone",
        category: "beef",
        image: "assets/images/pakistani-beef-bone.jpg",
        description: "Authentic Pakistani beef available with bone or boneless. Rich flavor, perfect for traditional curries and steaks.",
        origin: "Pakistan",
        boneIn: true,
        detailPage: "beef-products.html"
    },

    // CHICKEN PRODUCTS - Representative Product
    {
        id: 4,
        name: "Fresh Chicken Collection",
        weight: "All cuts available",
        category: "chicken",
        image: "assets/images/chicken-whole.jpg",
        description: "Fresh chicken in all cuts - whole, breast, thighs, wings, drumsticks, and mixed pieces. Perfect for any cooking style.",
        detailPage: "chicken-products.html"
    },

    // OFFAL PRODUCTS - Representative Product
    {
        id: 5,
        name: "Fresh Offal Selection",
        weight: "Available fresh daily",
        category: "offal",
        image: "assets/images/fresh-offal.jpg",
        description: "Fresh offal including liver, kidney, heart, and specialty cuts. Traditional delicacies for authentic cooking.",
        detailPage: "offal-products.html"
    }
];

// DOM elements
const hamburger = document.querySelector('.hamburger');
const navMenu = document.querySelector('.nav-menu');
const productsGrid = document.getElementById('products-grid');
const filterButtons = document.querySelectorAll('.filter-btn');

// Initialize the website
document.addEventListener('DOMContentLoaded', function() {
    initializeNavigation();
    if (productsGrid) {
        displayProducts('all');
        initializeFilters();
    }
    initializeSmoothScrolling();
});

// Navigation functionality
function initializeNavigation() {
    if (hamburger && navMenu) {
        hamburger.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            hamburger.classList.toggle('active');
        });

        // Close mobile menu when clicking on a link
        document.querySelectorAll('.nav-menu a').forEach(link => {
            link.addEventListener('click', () => {
                navMenu.classList.remove('active');
                hamburger.classList.remove('active');
            });
        });
    }
}

// Product display functionality 
function displayProducts(category) {
    if (!productsGrid) return;
    
    const filteredProducts = category === 'all' 
        ? products 
        : products.filter(product => product.category === category);

    productsGrid.innerHTML = filteredProducts.map(product => {
        const clickableCategories = ['chicken', 'lamb', 'beef', 'mutton', 'offal'];
        const isClickable = clickableCategories.includes(product.category);
        const cardClass = isClickable ? 'product-card clickable-card' : 'product-card';

        let clickHandler = '';
        let viewMoreButton = '';
        let clickHint = '';

        if (isClickable) {
            const categoryPages = {
                'chicken': 'chicken-products.html',
                'lamb': 'lamb-products.html',
                'beef': 'beef-products.html',
                'mutton': 'mutton-products.html',
                'offal': 'offal-products.html'
            };

            const categoryLabels = {
                'chicken': 'Chicken',
                'lamb': 'Lamb',
                'beef': 'Beef',
                'mutton': 'Mutton',
                'offal': 'Offal'
            };

            clickHandler = `onclick="window.location.href='${categoryPages[product.category]}'"`;
            viewMoreButton = `<div class="view-more-btn">🔍 View All ${categoryLabels[product.category]} Products</div>`;
            clickHint = `<p class="click-hint">👆 Click to see all ${categoryLabels[product.category].toLowerCase()} varieties</p>`;
        }

        return `
        <div class="${cardClass}" data-category="${product.category}" ${clickHandler}>
            <div class="product-image">
                <img src="${product.image}" alt="${product.name}" onerror="this.src='assets/images/placeholder.jpg'">
                <div class="halal-icon">Halal ✓</div>
                ${product.airFlown ? '<div class="air-flown-badge">✈️ Air Flown</div>' : ''}
                ${viewMoreButton}
            </div>
            <div class="product-info">
                <h3 class="product-name">${product.name}</h3>
                ${product.origin ? `<p class="product-origin">Origin: ${product.origin}</p>` : ''}
                <p class="product-weight">${product.weight}</p>
                ${product.boneIn !== undefined ? `<p class="bone-info">${product.boneIn ? 'With Bone' : 'Boneless'}</p>` : ''}
                <p class="product-description">${product.description}</p>
                ${clickHint}
            </div>
        </div>
        `;
    }).join('');
}

// Filter functionality
function initializeFilters() {
    if (!filterButtons.length) return;
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            filterButtons.forEach(btn => btn.classList.remove('active'));
            
            // Add active class to clicked button
            this.classList.add('active');
            
            // Filter products
            const category = this.getAttribute('data-filter');
            displayProducts(category);
        });
    });
}

// Smooth scrolling for navigation links
function initializeSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Category card click functionality (for homepage)
document.addEventListener('DOMContentLoaded', function() {
    const categoryCards = document.querySelectorAll('.category-card');
    
    categoryCards.forEach(card => {
        card.addEventListener('click', function() {
            const category = this.getAttribute('data-category');
            
            // If we're on the homepage, scroll to products section
            const productsSection = document.getElementById('products');
            if (productsSection) {
                productsSection.scrollIntoView({
                    behavior: 'smooth'
                });
                
                // Filter products after a short delay
                setTimeout(() => {
                    // Update filter buttons
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    const targetButton = document.querySelector(`[data-filter="${category}"]`);
                    if (targetButton) {
                        targetButton.classList.add('active');
                    }
                    
                    // Display filtered products
                    displayProducts(category);
                }, 500);
            }
        });
    });
});

// WhatsApp integration
function openWhatsApp(message = "Hello! I'm interested in your halal meat products. Could you please provide more information about availability and pricing?") {
    const phoneNumber = "971XXXXXXXXX"; // Replace with actual WhatsApp number
    const encodedMessage = encodeURIComponent(message);
    const whatsappURL = `https://wa.me/${phoneNumber}?text=${encodedMessage}`;
    window.open(whatsappURL, '_blank');
}

// Add WhatsApp functionality to buttons
document.addEventListener('DOMContentLoaded', function() {
    const whatsappButtons = document.querySelectorAll('.whatsapp-btn');
    whatsappButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            openWhatsApp();
        });
    });
});

// Intersection Observer for animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver(function(entries) {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
        }
    });
}, observerOptions);

// Observe elements for animation
document.addEventListener('DOMContentLoaded', function() {
    const animateElements = document.querySelectorAll('.category-card, .product-card, .review-card, .offer-card');
    
    animateElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
});
*/

// Qayyum Meat House - Featured Product Categories (Simplified)
const products = [
    // LAMB PRODUCTS - Representative Product
    {
        id: 1,
        name: "Premium Lamb Collection",
        weight: "Available in various cuts",
        category: "lamb",
        image: "assets/images/australian-lamb.jpg",
        description: "Premium lamb varieties including Australian, Kenya, and South African lamb - all air-flown for freshness.",
        origin: "Multiple Origins",
        airFlown: true,
        detailPage: "lamb-products.html"
    },

    // GOAT PRODUCTS - Representative Product
    {
        id: 2,
        name: "Fresh Goat Meat",
        weight: "Available in various cuts",
        category: "mutton",
        image: "assets/images/kinea-goat.jpg",
        description: "Fresh Kenya goat meat, air-transported for premium quality. Perfect for traditional cooking.",
        origin: "Kenya",
        airFlown: true,
        detailPage: "mutton-products.html"
    },

    // BEEF PRODUCTS - Representative Product
    {
        id: 3,
        name: "Pakistani Beef Collection",
        weight: "Available with/without bone",
        category: "beef",
        image: "assets/images/beef1.avif",
        description: "Authentic Pakistani beef available with bone or boneless. Rich flavor, perfect for traditional curries and steaks.",
        origin: "Pakistan",
       /*boneIn: true,*/
        detailPage: "beef-products.html"
    },

    // CHICKEN PRODUCTS - Representative Product
    {
        id: 4,
        name: "Fresh Chicken Collection",
        weight: "All cuts available",
        category: "chicken",
        image: "assets/images/chicken-whole.jpg",
        description: "Fresh chicken in all cuts - whole, breast, thighs, wings, drumsticks, and mixed pieces. Perfect for any cooking style.",
        detailPage: "chicken-products.html"
    },

    // OFFAL PRODUCTS - Representative Product
    {
        id: 5,
        name: "Fresh Offal Selection",
        weight: "Available fresh daily",
        category: "offal",
        image: "assets/images/offal2.jpg",
        description: "Fresh offal including liver, kidney, heart, and specialty cuts. Traditional delicacies for authentic cooking.",
        detailPage: "offal-products.html"
    }
];

// DOM elements
const hamburger = document.querySelector('.hamburger');
const navMenu = document.querySelector('.nav-menu');
const productsGrid = document.getElementById('products-grid');
const filterButtons = document.querySelectorAll('.filter-btn');

// Initialize the website
document.addEventListener('DOMContentLoaded', function() {
    initializeNavigation();
    displayProducts('all');
    initializeFilters();
    initializeSmoothScrolling();
});

// Navigation functionality
function initializeNavigation() {
    hamburger.addEventListener('click', function() {
        navMenu.classList.toggle('active');
        
        // Animate hamburger
        hamburger.classList.toggle('active');
    });

    // Close mobile menu when clicking on a link
    document.querySelectorAll('.nav-menu a').forEach(link => {
        link.addEventListener('click', () => {
            navMenu.classList.remove('active');
            hamburger.classList.remove('active');
        });
    });
}

// Product display functionality
function displayProducts(category) {
    const filteredProducts = category === 'all'
        ? products
        : products.filter(product => product.category === category);

    productsGrid.innerHTML = filteredProducts.map(product => `
        <div class="product-card" data-category="${product.category}">
            <div class="product-image">
                <img src="${product.image}" alt="${product.name}" onerror="this.src='assets/images/placeholder.jpg'">
                <div class="halal-icon">Halal ✓</div>
                ${product.airFlown ? '<div class="air-flown-badge">✈️ Air Flown</div>' : ''}
            </div>
            <div class="product-info">
                <h3 class="product-name">${product.name}</h3>
                ${product.origin ? `<p class="product-origin">Origin: ${product.origin}</p>` : ''}
                <p class="product-weight">${product.weight}</p>
                ${product.boneIn !== undefined ? `<p class="bone-info">${product.boneIn ? 'With Bone' : 'Boneless'}</p>` : ''}
                <p class="product-description">${product.description}</p>
                ${product.detailPage ? `
                    <div class="product-actions">
                        <button class="discover-btn" onclick="window.location.href='${product.detailPage}'">
                            <span>Discover More</span>
                            <i class="arrow-icon">→</i>
                        </button>
                    </div>
                ` : ''}
            </div>
        </div>
    `).join('');
}

// Filter functionality
function initializeFilters() {
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            filterButtons.forEach(btn => btn.classList.remove('active'));
            
            // Add active class to clicked button
            this.classList.add('active');
            
            // Filter products
            const category = this.getAttribute('data-filter');
            displayProducts(category);
        });
    });
}



// Smooth scrolling for navigation links
function initializeSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Category card click functionality
document.addEventListener('DOMContentLoaded', function() {
    const categoryCards = document.querySelectorAll('.category-card');
    
    categoryCards.forEach(card => {
        card.addEventListener('click', function() {
            const category = this.getAttribute('data-category');
            
            // Scroll to products section
            document.getElementById('products').scrollIntoView({
                behavior: 'smooth'
            });
            
            // Filter products after a short delay
            setTimeout(() => {
                // Update filter buttons
                filterButtons.forEach(btn => btn.classList.remove('active'));
                const targetButton = document.querySelector(`[data-filter="${category}"]`);
                if (targetButton) {
                    targetButton.classList.add('active');
                }
                
                // Display filtered products
                displayProducts(category);
            }, 500);
        });
    });
});

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    .hamburger.active span:nth-child(1) {
        transform: rotate(-45deg) translate(-5px, 6px);
    }
    
    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }
    
    .hamburger.active span:nth-child(3) {
        transform: rotate(45deg) translate(-5px, -6px);
    }
    
    .product-card {
        transition: all 0.3s ease;
    }
    
    .product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    }
    
    .product-description {
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 1rem;
        line-height: 1.4;
    }
`;

document.head.appendChild(style);

// WhatsApp integration
function openWhatsApp(message = "Hello! I'm interested in your halal meat products.") {
    const phoneNumber = "971XXXXXXXXX"; // Replace with actual WhatsApp number
    const encodedMessage = encodeURIComponent(message);
    const whatsappURL = `https://wa.me/${phoneNumber}?text=${encodedMessage}`;
    window.open(whatsappURL, '_blank');
}

// Add WhatsApp functionality to buttons
document.addEventListener('DOMContentLoaded', function() {
    const whatsappButtons = document.querySelectorAll('.whatsapp-btn');
    whatsappButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            openWhatsApp();
        });
    });
});

// Intersection Observer for animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver(function(entries) {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
        }
    });
}, observerOptions);

// Observe elements for animation
document.addEventListener('DOMContentLoaded', function() {
    const animateElements = document.querySelectorAll('.category-card, .product-card, .review-card, .offer-card');
    
    animateElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
});

// DARK MODE TOGGLE SCRIPT
document.addEventListener('DOMContentLoaded', function () {
  const toggleBtn = document.getElementById('theme-toggle');
  const body = document.body;
  const icon = document.getElementById('toggle-icon');

  // Load saved theme
  const savedTheme = localStorage.getItem('theme');
  if (savedTheme === 'dark') {
    body.classList.add('dark-mode');
    icon.textContent = '☀️';
  } else {
    icon.textContent = '🌙';
  }

  toggleBtn.addEventListener('click', () => {
    body.classList.toggle('dark-mode');
    const isDark = body.classList.contains('dark-mode');
    icon.textContent = isDark ? '☀️' : '🌙';
    localStorage.setItem('theme', isDark ? 'dark' : 'light');
  });
});
