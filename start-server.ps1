# Qayyum Meat House - Website Server Launcher (PowerShell)
# Run this script to start the local development server

Write-Host ""
Write-Host "========================================" -ForegroundColor Red
Write-Host "   Qayyum Meat House - Website Server" -ForegroundColor Red
Write-Host "========================================" -ForegroundColor Red
Write-Host ""

# Check if Python is installed
try {
    $pythonVersion = python --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "Python not found"
    }
    Write-Host "✅ Python found: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ ERROR: Python is not installed or not in PATH" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please install Python from: https://python.org" -ForegroundColor Yellow
    Write-Host "Make sure to check 'Add Python to PATH' during installation" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if index.html exists
if (-not (Test-Path "index.html")) {
    Write-Host "❌ ERROR: index.html not found in current directory" -ForegroundColor Red
    Write-Host "💡 Make sure you're running this from the website root directory" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "🚀 Starting local development server..." -ForegroundColor Green
Write-Host ""
Write-Host "📍 The website will be available at: http://localhost:8000" -ForegroundColor Cyan
Write-Host "🌐 The website will open automatically in your browser" -ForegroundColor Cyan
Write-Host "⏹️  Press Ctrl+C to stop the server" -ForegroundColor Yellow
Write-Host ""

# Start the Python server
try {
    python server.py
} catch {
    Write-Host ""
    Write-Host "❌ Error starting server: $_" -ForegroundColor Red
    Write-Host ""
    Write-Host "💡 Trying alternative method..." -ForegroundColor Yellow
    python -m http.server 8000
}

Write-Host ""
Read-Host "Press Enter to exit"
