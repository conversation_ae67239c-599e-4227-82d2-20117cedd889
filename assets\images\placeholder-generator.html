<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Placeholder Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .placeholder {
            width: 300px;
            height: 200px;
            background: linear-gradient(135deg, #e60000, #cc0000);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 10px;
            border-radius: 10px;
            font-weight: bold;
            text-align: center;
            cursor: pointer;
        }
        .grid {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        h1 {
            color: #e60000;
            text-align: center;
        }
        p {
            text-align: center;
            color: #666;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><PERSON><PERSON><PERSON><PERSON> House - Image Placeholders</h1>
        <p>Right-click on any placeholder and "Save image as..." to download</p>
        
        <div class="grid">
            <canvas class="placeholder" width="600" height="400" data-text="Hero Meat Selection"></canvas>
            <canvas class="placeholder" width="600" height="400" data-text="About Shop Image"></canvas>
            <canvas class="placeholder" width="600" height="400" data-text="Premium Beef Steak"></canvas>
            <canvas class="placeholder" width="600" height="400" data-text="Fresh Chicken Breast"></canvas>
            <canvas class="placeholder" width="600" height="400" data-text="Tender Lamb Chops"></canvas>
            <canvas class="placeholder" width="600" height="400" data-text="Ground Beef"></canvas>
            <canvas class="placeholder" width="600" height="400" data-text="Whole Chicken"></canvas>
            <canvas class="placeholder" width="600" height="400" data-text="Mutton Curry Cut"></canvas>
            <canvas class="placeholder" width="600" height="400" data-text="Beef Ribs"></canvas>
            <canvas class="placeholder" width="600" height="400" data-text="Lamb Leg"></canvas>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const canvases = document.querySelectorAll('canvas.placeholder');
            
            canvases.forEach(canvas => {
                const ctx = canvas.getContext('2d');
                const text = canvas.getAttribute('data-text');
                
                // Create gradient background
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#e60000');
                gradient.addColorStop(1, '#cc0000');
                
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // Add text
                ctx.fillStyle = 'white';
                ctx.font = 'bold 32px Arial';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                
                // Word wrap for long text
                const words = text.split(' ');
                const lines = [];
                let currentLine = words[0];
                
                for (let i = 1; i < words.length; i++) {
                    const word = words[i];
                    const width = ctx.measureText(currentLine + " " + word).width;
                    if (width < canvas.width - 40) {
                        currentLine += " " + word;
                    } else {
                        lines.push(currentLine);
                        currentLine = word;
                    }
                }
                lines.push(currentLine);
                
                // Draw lines
                const lineHeight = 40;
                const startY = canvas.height / 2 - (lines.length - 1) * lineHeight / 2;
                
                lines.forEach((line, index) => {
                    ctx.fillText(line, canvas.width / 2, startY + index * lineHeight);
                });
                
                // Add halal symbol
                ctx.font = 'bold 24px Arial';
                ctx.fillText('✓ HALAL', canvas.width / 2, canvas.height - 30);
            });
        });
    </script>
</body>
</html>
