// Beef Product Gallery System
document.addEventListener('DOMContentLoaded', function() {
    // Product image galleries - you can add more images for each product
    const productGalleries = {
        'beef-with-bone': [
            { src: 'assets/images/beef-with-bone.jpg', alt: 'Pakistani Beef with Bone - Main View' },
            { src: 'assets/images/beef-with-bone.jpg', alt: 'Pakistani Beef with Bone - Cut View' },
            { src: 'assets/images/beef-with-bone.jpg', alt: 'Pakistani Beef with Bone - Quality Check' },
            { src: 'assets/images/beef-with-bone.jpg', alt: 'Pakistani Beef with Bone - Traditional Cut' }
        ],
        'beef-boneless': [
            { src: 'assets/images/beef-boneless.jpg', alt: 'Pakistani Beef Boneless - Main View' },
            { src: 'assets/images/beef-boneless.jpg', alt: 'Pakistani Beef Boneless - Clean Cut' },
            { src: 'assets/images/beef-boneless.jpg', alt: 'Pakistani Beef Boneless - Ready to Cook' },
            { src: 'assets/images/beef-boneless.jpg', alt: 'Pakistani Beef Boneless - Premium Quality' }
        ],
        'beef-steaks': [
            { src: 'assets/images/beef-steaks.jpg', alt: 'Beef Steaks - Main View' },
            { src: 'assets/images/beef-steaks.jpg', alt: 'Beef Steaks - Grill Ready' },
            { src: 'assets/images/beef-steaks.jpg', alt: 'Beef Steaks - Premium Cut' },
            { src: 'assets/images/beef-steaks.jpg', alt: 'Beef Steaks - Perfect Thickness' }
        ],
        'ground-beef': [
            { src: 'assets/images/ground-beef.jpg', alt: 'Ground Beef - Main View' },
            { src: 'assets/images/ground-beef.jpg', alt: 'Ground Beef - Fresh Ground' },
            { src: 'assets/images/ground-beef.jpg', alt: 'Ground Beef - Keema Ready' },
            { src: 'assets/images/ground-beef.jpg', alt: 'Ground Beef - Family Pack' }
        ],
        'beef-ribs': [
            { src: 'assets/images/beef-ribs.jpg', alt: 'Beef Ribs - Main View' },
            { src: 'assets/images/beef-ribs.jpg', alt: 'Beef Ribs - BBQ Ready' },
            { src: 'assets/images/beef-ribs.jpg', alt: 'Beef Ribs - Full Rack' },
            { src: 'assets/images/beef-ribs.jpg', alt: 'Beef Ribs - Tender Cut' }
        ],
        'beef-brisket': [
            { src: 'assets/images/beef-brisket.jpg', alt: 'Beef Brisket - Main View' },
            { src: 'assets/images/beef-brisket.jpg', alt: 'Beef Brisket - Specialty Cut' },
            { src: 'assets/images/beef-brisket.jpg', alt: 'Beef Brisket - Slow Cook Ready' },
            { src: 'assets/images/beef-brisket.jpg', alt: 'Beef Brisket - Premium Quality' }
        ]
    };

    // Product names for modal titles
    const productNames = {
        'beef-with-bone': 'Pakistani Beef with Bone Gallery',
        'beef-boneless': 'Pakistani Beef Boneless Gallery',
        'beef-steaks': 'Beef Steaks Gallery',
        'ground-beef': 'Ground Beef Gallery',
        'beef-ribs': 'Beef Ribs Gallery',
        'beef-brisket': 'Beef Brisket Gallery'
    };

    // Modal elements
    const modal = document.getElementById('productModal');
    const modalTitle = document.getElementById('modalTitle');
    const mainImage = document.getElementById('mainImage');
    const currentImageSpan = document.getElementById('currentImage');
    const totalImagesSpan = document.getElementById('totalImages');
    const thumbnailGrid = document.getElementById('thumbnailGrid');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const closeBtn = document.querySelector('.close');

    let currentProduct = '';
    let currentImageIndex = 0;
    let currentGallery = [];

    // Add click event listeners to all product cards
    const productCards = document.querySelectorAll('.clickable-product');
    productCards.forEach(card => {
        card.addEventListener('click', function() {
            const productType = this.getAttribute('data-product');
            openGallery(productType);
        });
    });

    // Open gallery function
    function openGallery(productType) {
        if (!productGalleries[productType]) return;

        currentProduct = productType;
        currentGallery = productGalleries[productType];
        currentImageIndex = 0;

        // Set modal title
        modalTitle.textContent = productNames[productType] || 'Product Gallery';

        // Set total images
        totalImagesSpan.textContent = currentGallery.length;

        // Create thumbnails
        createThumbnails();

        // Show first image
        showImage(0);

        // Show modal
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden'; // Prevent background scrolling
    }

    // Create thumbnail images
    function createThumbnails() {
        thumbnailGrid.innerHTML = '';
        
        currentGallery.forEach((image, index) => {
            const thumbnail = document.createElement('img');
            thumbnail.src = image.src;
            thumbnail.alt = image.alt;
            thumbnail.className = 'thumbnail';
            thumbnail.addEventListener('click', () => showImage(index));
            
            if (index === 0) {
                thumbnail.classList.add('active');
            }
            
            thumbnailGrid.appendChild(thumbnail);
        });
    }

    // Show specific image
    function showImage(index) {
        if (index < 0 || index >= currentGallery.length) return;

        currentImageIndex = index;
        const image = currentGallery[index];

        // Update main image
        mainImage.src = image.src;
        mainImage.alt = image.alt;

        // Update counter
        currentImageSpan.textContent = index + 1;

        // Update active thumbnail
        const thumbnails = thumbnailGrid.querySelectorAll('.thumbnail');
        thumbnails.forEach((thumb, i) => {
            thumb.classList.toggle('active', i === index);
        });

        // Update navigation buttons
        prevBtn.disabled = index === 0;
        nextBtn.disabled = index === currentGallery.length - 1;
    }

    // Navigation event listeners
    prevBtn.addEventListener('click', () => {
        if (currentImageIndex > 0) {
            showImage(currentImageIndex - 1);
        }
    });

    nextBtn.addEventListener('click', () => {
        if (currentImageIndex < currentGallery.length - 1) {
            showImage(currentImageIndex + 1);
        }
    });

    // Close modal function
    function closeModal() {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto'; // Restore background scrolling
    }

    // Close modal event listeners
    closeBtn.addEventListener('click', closeModal);

    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal();
        }
    });

    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (modal.style.display === 'block') {
            switch(e.key) {
                case 'Escape':
                    closeModal();
                    break;
                case 'ArrowLeft':
                    if (currentImageIndex > 0) {
                        showImage(currentImageIndex - 1);
                    }
                    break;
                case 'ArrowRight':
                    if (currentImageIndex < currentGallery.length - 1) {
                        showImage(currentImageIndex + 1);
                    }
                    break;
            }
        }
    });

    // Touch/swipe support for mobile
    let touchStartX = 0;
    let touchEndX = 0;

    mainImage.addEventListener('touchstart', function(e) {
        touchStartX = e.changedTouches[0].screenX;
    });

    mainImage.addEventListener('touchend', function(e) {
        touchEndX = e.changedTouches[0].screenX;
        handleSwipe();
    });

    function handleSwipe() {
        const swipeThreshold = 50;
        const diff = touchStartX - touchEndX;

        if (Math.abs(diff) > swipeThreshold) {
            if (diff > 0 && currentImageIndex < currentGallery.length - 1) {
                // Swipe left - next image
                showImage(currentImageIndex + 1);
            } else if (diff < 0 && currentImageIndex > 0) {
                // Swipe right - previous image
                showImage(currentImageIndex - 1);
            }
        }
    }
});
