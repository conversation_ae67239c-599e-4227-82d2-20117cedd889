// Universal Product Gallery System for All Product Pages
document.addEventListener('DOMContentLoaded', function() {
    // Check if modal exists on this page
    const modal = document.getElementById('productModal');
    if (!modal) return;

    // Universal product galleries - works for all product types
    const productGalleries = {
        // Chicken Products
        'chicken-whole': [
            { src: 'assets/images/chicken-section/whole-chicken/chicken-whole.jpg', alt: 'Whole Chicken - Main View' },
            { src: 'assets/images/chicken-section/whole-chicken/chicken-whole.jpg', alt: 'Whole Chicken - Cleaned' },
            { src: 'assets/images/chicken-section/whole-chicken/chicken-whole.jpg', alt: 'Whole Chicken - Ready to Cook' },
            { src: 'assets/images/chicken-section/whole-chicken/chicken-whole.jpg', alt: 'Whole Chicken - Fresh Quality' }
        ],
        'chicken-breast': [
            { src: 'assets/images/chicken-section/chicken-breast/chicken-breast.jpg', alt: 'Chicken Breast - Main View' },
            { src: 'assets/images/chicken-section/chicken-breast/chicken-breast.jpg', alt: 'Chicken Breast - Boneless' },
            { src: 'assets/images/chicken-section/chicken-breast/chicken-breast.jpg', alt: 'Chicken Breast - Premium Cut' },
            { src: 'assets/images/chicken-section/chicken-breast/chicken-breast.jpg', alt: 'Chicken Breast - Ready to Cook' }
        ],
        'chicken-thighs': [
            { src: 'assets/images/chicken-section/chicken-thighs/chicken-thighs.jpg', alt: 'Chicken Thighs - Main View' },
            { src: 'assets/images/chicken-section/chicken-thighs/chicken-thighs.jpg', alt: 'Chicken Thighs - Juicy Cut' },
            { src: 'assets/images/chicken-section/chicken-thighs/chicken-thighs.jpg', alt: 'Chicken Thighs - Perfect Size' },
            { src: 'assets/images/chicken-section/chicken-thighs/chicken-thighs.jpg', alt: 'Chicken Thighs - Fresh Quality' }
        ],
        'chicken-wings': [
            { src: 'assets/images/chicken-section/chicken-wings/chicken-wings.jpg', alt: 'Chicken Wings - Main View' },
            { src: 'assets/images/chicken-section/chicken-wings/chicken-wings.jpg', alt: 'Chicken Wings - Party Pack' },
            { src: 'assets/images/chicken-section/chicken-wings/chicken-wings.jpg', alt: 'Chicken Wings - BBQ Ready' },
            { src: 'assets/images/chicken-section/chicken-wings/chicken-wings.jpg', alt: 'Chicken Wings - Fresh Cut' }
        ],
        'chicken-drumsticks': [
            { src: 'assets/images/chicken-section/chicken-drumstics/chicken-drumsticks.jpg', alt: 'Chicken Drumsticks - Main View' },
            { src: 'assets/images/chicken-section/chicken-drumstics/chicken-drumsticks.jpg', alt: 'Chicken Drumsticks - Family Pack' },
            { src: 'assets/images/chicken-section/chicken-drumstics/chicken-drumsticks.jpg', alt: 'Chicken Drumsticks - Fresh Quality' },
            { src: 'assets/images/chicken-section/chicken-drumstics/chicken-drumsticks.jpg', alt: 'Chicken Drumsticks - Perfect Size' }
        ],
        'chicken-mixed': [
            { src: 'assets/images/chicken-section/mixed-chicken-pieces/chicken-mixed.jpg', alt: 'Mixed Chicken Pieces - Main View' },
            { src: 'assets/images/chicken-section/mixed-chicken-pieces/chicken-mixed.jpg', alt: 'Mixed Chicken Pieces - Variety Pack' },
            { src: 'assets/images/chicken-section/mixed-chicken-pieces/chicken-mixed.jpg', alt: 'Mixed Chicken Pieces - Family Size' },
            { src: 'assets/images/chicken-section/mixed-chicken-pieces/chicken-mixed.jpg', alt: 'Mixed Chicken Pieces - Fresh Cut' }
        ],

        // Lamb Products
        'australian-lamb': [
            { src: 'assets/images/lamb-section/australian/Aus-lamb2.jpg', alt: 'Australian Lamb - Main View' },
            { src: 'assets/images/lamb-section/australian/Aus-lamb.jpg', alt: 'Australian Lamb - Premium Grade' },
            { src: 'assets/images/lamb-section/australian/Aus-lamb.jpg', alt: 'Australian Lamb - Air Flown Fresh' },
            { src: 'assets/images/lamb-section/australian/Aus-lamb.jpg', alt: 'Australian Lamb - Grass Fed' }
        ],
        'kenya-lamb': [
            { src: 'assets/images/lamb-section/kenya/kenya-lamb2.jpg', alt: 'Kenya Lamb - Main View' },
            { src: 'assets/images/lamb-section/kenya/kenya-lamb.jpg', alt: 'Kenya Lamb - Highland Raised' },
            { src: 'assets/images/lamb-section/kenya/kenya-lamb.jpg', alt: 'Kenya Lamb - Air Flown Fresh' },
            { src: 'assets/images/lamb-section/kenya/kenya-lamb.jpg', alt: 'Kenya Lamb - Authentic Taste' }
        ],
        'south-african-lamb': [
            { src: 'assets/images/african-lamb.jpg', alt: 'South African Lamb - Main View' },
            { src: 'assets/images/african-lamb.jpg', alt: 'South African Lamb - Rich Flavor' },
            { src: 'assets/images/african-lamb.jpg', alt: 'South African Lamb - Top Grade' },
            { src: 'assets/images/african-lamb.jpg', alt: 'South African Lamb - Air Flown' }
        ],
        'lamb-shoulder': [
            { src: 'assets/images/lamb-section/shoulder/lamb-shoulder.jpg', alt: 'Lamb Shoulder - Main View' },
            { src: 'assets/images/lamb-section/shoulder/lamb-shoulder.jpg', alt: 'Lamb Shoulder - Perfect for Roasting' },
            { src: 'assets/images/lamb-section/shoulder/lamb-shoulder.jpg', alt: 'Lamb Shoulder - Slow Cook Ready' },
            { src: 'assets/images/lamb-section/shoulder/lamb-shoulder.jpg', alt: 'Lamb Shoulder - Premium Cut' }
        ],
        'lamb-leg': [
            { src: 'assets/images/lamb-section/leg/lamb-leg.jpg', alt: 'Lamb Leg - Main View' },
            { src: 'assets/images/lamb-section/leg/lamb-leg.jpg', alt: 'Lamb Leg - Family Size' },
            { src: 'assets/images/lamb-section/leg/lamb-leg.jpg', alt: 'Lamb Leg - Special Occasions' },
            { src: 'assets/images/lamb-section/leg/lamb-leg.jpg', alt: 'Lamb Leg - Premium Quality' }
        ],
        'lamb-chops': [
            { src: 'assets/images/lamb-section/chops/lamb-chops.jpg', alt: 'Lamb Chops - Main View' },
            { src: 'assets/images/lamb-section/chops/lamb-chops.jpg', alt: 'Lamb Chops - Grill Perfect' },
            { src: 'assets/images/lamb-section/chops/lamb-chops.jpg', alt: 'Lamb Chops - Restaurant Quality' },
            { src: 'assets/images/lamb-section/chops/lamb-chops.jpg', alt: 'Lamb Chops - Premium Cut' }
        ]
    };

    // Universal product names
    const productNames = {
        // Chicken
        'chicken-whole': 'Whole Chicken Gallery',
        'chicken-breast': 'Chicken Breast Gallery',
        'chicken-thighs': 'Chicken Thighs Gallery',
        'chicken-wings': 'Chicken Wings Gallery',
        'chicken-drumsticks': 'Chicken Drumsticks Gallery',
        'chicken-mixed': 'Mixed Chicken Pieces Gallery',
        
        // Lamb
        'australian-lamb': 'Australian Lamb Gallery',
        'kenya-lamb': 'Kenya Lamb Gallery',
        'south-african-lamb': 'South African Lamb Gallery',
        'lamb-shoulder': 'Lamb Shoulder Gallery',
        'lamb-leg': 'Lamb Leg Gallery',
        'lamb-chops': 'Lamb Chops Gallery'
    };

    // Modal elements
    const modalTitle = document.getElementById('modalTitle');
    const mainImage = document.getElementById('mainImage');
    const currentImageSpan = document.getElementById('currentImage');
    const totalImagesSpan = document.getElementById('totalImages');
    const thumbnailGrid = document.getElementById('thumbnailGrid');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const closeBtn = document.querySelector('.close');

    let currentProduct = '';
    let currentImageIndex = 0;
    let currentGallery = [];

    // Add click event listeners to all product cards
    const productCards = document.querySelectorAll('.clickable-product');
    productCards.forEach(card => {
        card.addEventListener('click', function() {
            const productType = this.getAttribute('data-product');
            openGallery(productType);
        });
    });

    // Open gallery function
    function openGallery(productType) {
        if (!productGalleries[productType]) {
            // Fallback for products not in the gallery list
            console.log('Gallery not found for:', productType);
            return;
        }

        currentProduct = productType;
        currentGallery = productGalleries[productType];
        currentImageIndex = 0;

        // Set modal title
        modalTitle.textContent = productNames[productType] || 'Product Gallery';

        // Set total images
        totalImagesSpan.textContent = currentGallery.length;

        // Create thumbnails
        createThumbnails();

        // Show first image
        showImage(0);

        // Show modal
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }

    // Create thumbnail images
    function createThumbnails() {
        thumbnailGrid.innerHTML = '';
        
        currentGallery.forEach((image, index) => {
            const thumbnail = document.createElement('img');
            thumbnail.src = image.src;
            thumbnail.alt = image.alt;
            thumbnail.className = 'thumbnail';
            thumbnail.addEventListener('click', () => showImage(index));
            
            if (index === 0) {
                thumbnail.classList.add('active');
            }
            
            thumbnailGrid.appendChild(thumbnail);
        });
    }

    // Show specific image
    function showImage(index) {
        if (index < 0 || index >= currentGallery.length) return;

        currentImageIndex = index;
        const image = currentGallery[index];

        // Update main image
        mainImage.src = image.src;
        mainImage.alt = image.alt;

        // Update counter
        currentImageSpan.textContent = index + 1;

        // Update active thumbnail
        const thumbnails = thumbnailGrid.querySelectorAll('.thumbnail');
        thumbnails.forEach((thumb, i) => {
            thumb.classList.toggle('active', i === index);
        });

        // Update navigation buttons
        prevBtn.disabled = index === 0;
        nextBtn.disabled = index === currentGallery.length - 1;
    }

    // Navigation event listeners
    prevBtn.addEventListener('click', () => {
        if (currentImageIndex > 0) {
            showImage(currentImageIndex - 1);
        }
    });

    nextBtn.addEventListener('click', () => {
        if (currentImageIndex < currentGallery.length - 1) {
            showImage(currentImageIndex + 1);
        }
    });

    // Close modal function
    function closeModal() {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }

    // Close modal event listeners
    closeBtn.addEventListener('click', closeModal);

    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal();
        }
    });

    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (modal.style.display === 'block') {
            switch(e.key) {
                case 'Escape':
                    closeModal();
                    break;
                case 'ArrowLeft':
                    if (currentImageIndex > 0) {
                        showImage(currentImageIndex - 1);
                    }
                    break;
                case 'ArrowRight':
                    if (currentImageIndex < currentGallery.length - 1) {
                        showImage(currentImageIndex + 1);
                    }
                    break;
            }
        }
    });

    // Touch/swipe support for mobile
    let touchStartX = 0;
    let touchEndX = 0;

    if (mainImage) {
        mainImage.addEventListener('touchstart', function(e) {
            touchStartX = e.changedTouches[0].screenX;
        });

        mainImage.addEventListener('touchend', function(e) {
            touchEndX = e.changedTouches[0].screenX;
            handleSwipe();
        });
    }

    function handleSwipe() {
        const swipeThreshold = 50;
        const diff = touchStartX - touchEndX;

        if (Math.abs(diff) > swipeThreshold) {
            if (diff > 0 && currentImageIndex < currentGallery.length - 1) {
                showImage(currentImageIndex + 1);
            } else if (diff < 0 && currentImageIndex > 0) {
                showImage(currentImageIndex - 1);
            }
        }
    }
});
