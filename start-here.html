<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON> House - Server Launcher</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #e60000, #cc0000);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            padding: 40px;
            border-radius: 20px;
            text-align: center;
            max-width: 600px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }
        h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        .instructions {
            background: rgba(255,255,255,0.2);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: left;
        }
        .command {
            background: rgba(0,0,0,0.3);
            padding: 10px 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            border-left: 4px solid #ffd700;
        }
        .step {
            margin: 15px 0;
            padding: 10px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
        }
        .step-number {
            background: #ffd700;
            color: #000;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        .warning {
            background: rgba(255,193,7,0.2);
            border: 1px solid #ffd700;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .success {
            background: rgba(40,167,69,0.2);
            border: 1px solid #28a745;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🥩 Qayyum Meat House</h1>
        <p class="subtitle">Website Server Launcher</p>
        
        <div class="instructions">
            <h3>🚀 How to Start the Website:</h3>
            
            <div class="step">
                <span class="step-number">1</span>
                <strong>Open PowerShell or Command Prompt in this folder</strong>
            </div>
            
            <div class="step">
                <span class="step-number">2</span>
                <strong>Run one of these commands:</strong>
                
                <div class="command">
                    <strong>Option 1 (Recommended):</strong><br>
                    python server.py
                </div>
                
                <div class="command">
                    <strong>Option 2 (Simple):</strong><br>
                    python -m http.server 8000
                </div>
                
                <div class="command">
                    <strong>Option 3 (PowerShell Script):</strong><br>
                    .\start-server.ps1
                </div>
                
                <div class="command">
                    <strong>Option 4 (Batch File):</strong><br>
                    .\start-server.bat
                </div>
            </div>
            
            <div class="step">
                <span class="step-number">3</span>
                <strong>Open your browser and go to:</strong>
                <div class="command">http://localhost:8000</div>
            </div>
        </div>
        
        <div class="warning">
            <strong>⚠️ Important:</strong> Make sure Python is installed on your system. 
            If you get a "python is not recognized" error, install Python from 
            <a href="https://python.org" style="color: #ffd700;">python.org</a>
        </div>
        
        <div class="success">
            <strong>✅ Quick Test:</strong> If you see "Serving HTTP on..." message, 
            the server is running successfully!
        </div>
        
        <div class="instructions">
            <h3>🛠️ Troubleshooting:</h3>
            <ul style="text-align: left;">
                <li><strong>Port already in use:</strong> Try port 8001, 8080, or 3000</li>
                <li><strong>Python not found:</strong> Install Python and add to PATH</li>
                <li><strong>Permission denied:</strong> Run as administrator</li>
                <li><strong>Browser doesn't open:</strong> Manually go to http://localhost:8000</li>
            </ul>
        </div>
        
        <p style="margin-top: 30px; opacity: 0.8;">
            <strong>Pure. Halal. Fresh</strong> - From Our Market to Your Plate
        </p>
    </div>
</body>
</html>
