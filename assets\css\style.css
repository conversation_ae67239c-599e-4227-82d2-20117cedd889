/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
    background-image:
        linear-gradient(135deg, rgba(230, 0, 0, 0.1) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(230, 0, 0, 0.05) 100%),
        url('/assets/images/shop.jpg');
    background-attachment: fixed;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    position: relative;
    min-height: 100vh;
    background-color: #f8f9fa;
}

/* Background overlay with gradient for embedded feel */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(ellipse at center, transparent 0%, rgba(0, 0, 0, 0.1) 100%),
        linear-gradient(
            45deg,
            rgba(255, 255, 255, 0.15) 0%,
            rgba(230, 0, 0, 0.05) 25%,
            rgba(255, 255, 255, 0.1) 50%,
            rgba(230, 0, 0, 0.03) 75%,
            rgba(255, 255, 255, 0.12) 100%
        );
    z-index: -1;
    pointer-events: none;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Color Variables */
:root {
    --primary-red: #e60000;
    --dark-red: #cc0000;
    --black: #1a1a1a;
    --white: #ffffff;
    --light-gray: #f8f9fa;
    --gray: #6c757d;
    --gold: #ffd700;
}

/* Navigation */
.navbar {
    background: var(--white);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.nav-logo h2 {
    color: var(--primary-red);
    font-weight: 700;
    font-size: 1.5rem;
}

.halal-badge {
    background: var(--gold);
    color: var(--black);
    padding: 0.2rem 0.5rem;
    border-radius: 15px;
    font-size: 0.7rem;
    font-weight: 600;
    margin-left: 0.5rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
    align-items: center;
}

.nav-menu a {
    text-decoration: none;
    color: var(--black);
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-menu a:hover {
    color: var(--primary-red);
}

.cart-icon {
    position: relative;
    font-size: 1.2rem;
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--primary-red);
    color: var(--white);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--black);
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    display: flex;
    align-items: center;
    min-height: 100vh;
    padding: 120px 2rem 2rem;
    background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.5) 0%,
        rgba(230, 0, 0, 0.1) 50%,
        rgba(255, 255, 255, 0.4) 100%
    );
    backdrop-filter: blur(2px);
}

.hero-content {
    flex: 1;
    max-width: 600px;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--black);
    margin-bottom: 1rem;
    line-height: 1.2;
    text-shadow: 2px 2px 4px rgba(255, 255, 255, 0.8);
}

.hero-subtitle {
    font-size: 1.5rem;
    color: var(--gray);
    margin-bottom: 2rem;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
}

.hero-badges {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.badge {
    background: var(--primary-red);
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 500;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    padding: 1rem 2rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    cursor: pointer;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: var(--primary-red);
    color: var(--white);
    border-color: var(--primary-red);
}

.btn-primary:hover {
    background: var(--dark-red);
    transform: translateY(-2px);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-red);
    border-color: var(--primary-red);
}

.btn-secondary:hover {
    background: var(--primary-red);
    color: var(--white);
}

.btn-outline {
    background: transparent;
    color: var(--black);
    border-color: var(--black);
    padding: 0.8rem 1.5rem;
}

.btn-outline:hover {
    background: var(--black);
    color: var(--white);
}

/* ===== MODERN HERO IMAGE LAYOUT ===== */
.hero-image-container {
    flex: 1;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
}

.hero-image-wrapper {
    position: relative;
    max-width: 600px;
    width: 100%;
    transform: perspective(1000px) rotateY(-5deg) rotateX(2deg);
    transition: transform 0.3s ease;
}

.hero-image-wrapper:hover {
    transform: perspective(1000px) rotateY(0deg) rotateX(0deg) scale(1.02);
}

.hero-main-image {
    width: 100%;
    height: auto;
    border-radius: 25px;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.15),
        0 10px 25px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(231, 76, 60, 0.1) 0%,
        rgba(0, 0, 0, 0.1) 50%,
        rgba(231, 76, 60, 0.1) 100%
    );
    border-radius: 25px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 3;
    display: flex;
    align-items: flex-end;
    padding: 2rem;
}

.hero-image-wrapper:hover .image-overlay {
    opacity: 1;
}

.overlay-content {
    display: flex;
    gap: 1rem;
    width: 100%;
    justify-content: space-between;
}

.quality-badge,
.freshness-indicator {
    background: rgba(255, 255, 255, 0.95);
    padding: 0.8rem 1.2rem;
    border-radius: 50px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--black);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    transform: translateY(20px);
    transition: transform 0.3s ease 0.1s;
}

.hero-image-wrapper:hover .quality-badge,
.hero-image-wrapper:hover .freshness-indicator {
    transform: translateY(0);
}

.quality-badge i {
    color: var(--primary-red);
}

.freshness-indicator i {
    color: #28a745;
}

.image-frame {
    position: absolute;
    top: -15px;
    left: -15px;
    right: -15px;
    bottom: -15px;
    border: 3px solid rgba(231, 76, 60, 0.2);
    border-radius: 35px;
    z-index: 1;
    transition: all 0.3s ease;
}

.hero-image-wrapper:hover .image-frame {
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    border-color: rgba(231, 76, 60, 0.4);
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 4;
}

.floating-badge {
    position: absolute;
    background: white;
    padding: 1rem 1.5rem;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 0.8rem;
    font-weight: 600;
    font-size: 0.95rem;
    animation: float 3s ease-in-out infinite;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.floating-badge.halal-certified {
    top: 10%;
    right: -10%;
    color: var(--primary-red);
    animation-delay: 0s;
}

.floating-badge.waterfront-market {
    bottom: 15%;
    left: -15%;
    color: #007bff;
    animation-delay: 1.5s;
}

.floating-badge i {
    font-size: 1.2rem;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-10px) rotate(2deg);
    }
}

/* Section Titles */
.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--black);
    margin-bottom: 3rem;
}

/* Categories Section */
.categories {
    padding: 5rem 0;
    background: linear-gradient(
        45deg,
        rgba(255, 255, 255, 0.35) 0%,
        rgba(230, 0, 0, 0.05) 50%,
        rgba(255, 255, 255, 0.25) 100%
    );
    backdrop-filter: blur(3px);
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.category-card {
    background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.8) 0%,
        rgba(255, 255, 255, 0.6) 100%
    );
    backdrop-filter: blur(5px);
    padding: 2rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
    border: 1px solid rgba(255, 255, 255, 0.4);
}

.category-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.9) 0%,
        rgba(230, 0, 0, 0.05) 50%,
        rgba(255, 255, 255, 0.7) 100%
    );
}

.category-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.category-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--black);
    margin-bottom: 1rem;
}

.category-card p {
    color: var(--gray);
    margin-bottom: 1.5rem;
}

/* About Section */
.about {
    padding: 6rem 0;
    background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.8) 0%,
        rgba(230, 0, 0, 0.05) 50%,
        rgba(255, 255, 255, 0.7) 100%
    );
    backdrop-filter: blur(5px);
    text-align: center;
}

/* Centered About Content */
.about-content-centered {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 2rem;
}

.about-header {
    margin-bottom: 3rem;
    animation: fadeInUp 0.8s ease-out;
}

.about-title {
    font-size: 3rem;
    font-weight: 700;
    color: var(--black);
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(255, 255, 255, 0.8);
    line-height: 1.2;
}

.about-subtitle {
    font-size: 1.3rem;
    color: var(--primary-red);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 2px;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
}

.about-story {
    margin-bottom: 3rem;
    animation: fadeInUp 0.8s ease-out 0.2s both;
}

.about-intro {
    font-size: 1.3rem;
    color: var(--black);
    margin-bottom: 1.5rem;
    line-height: 1.8;
    font-weight: 500;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
}

.about-mission {
    font-size: 1.1rem;
    color: var(--gray);
    margin-bottom: 1.5rem;
    line-height: 1.7;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
}

.about-promise {
    font-size: 1.1rem;
    color: var(--gray);
    margin-bottom: 0;
    line-height: 1.7;
    font-style: italic;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
}

.trust-badges-centered {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.trust-badge {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.8rem;
    padding: 1.5rem;
    background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.9) 0%,
        rgba(255, 255, 255, 0.7) 100%
    );
    backdrop-filter: blur(10px);
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border: 1px solid rgba(255, 255, 255, 0.5);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.trust-badge:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.2);
}

.trust-badge i {
    color: var(--primary-red);
    font-size: 2rem;
}

.trust-badge span {
    font-weight: 600;
    color: var(--black);
    text-align: center;
    font-size: 0.9rem;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
}

.about-cta {
    margin-top: 2rem;
}

.cta-text {
    font-size: 1.2rem;
    color: var(--primary-red);
    font-weight: 600;
    font-style: italic;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
}

/* Products Showcase */
.products-showcase {
    padding: 5rem 0;
    background: linear-gradient(
        -45deg,
        rgba(255, 255, 255, 0.25) 0%,
        rgba(230, 0, 0, 0.08) 50%,
        rgba(255, 255, 255, 0.35) 100%
    );
    backdrop-filter: blur(3px);
}

.product-filters {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.filter-btn {
    background: transparent;
    border: 2px solid var(--gray);
    color: var(--gray);
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.filter-btn.active,
.filter-btn:hover {
    background: var(--primary-red);
    border-color: var(--primary-red);
    color: var(--white);
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.product-card {
    background: linear-gradient(
        145deg,
        rgba(255, 255, 255, 0.85) 0%,
        rgba(255, 255, 255, 0.75) 100%
    );
    backdrop-filter: blur(5px);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    transition: transform 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.4);
}

.product-card:hover {
    transform: translateY(-5px);
}

/* Clickable Product Cards */
.clickable-card {
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
}

.clickable-card:hover {
    transform: translateY(-15px);
    box-shadow: 0 25px 50px rgba(231, 76, 60, 0.3);
}

.clickable-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(231, 76, 60, 0.1), rgba(231, 76, 60, 0.05));
    border-radius: 15px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.clickable-card:hover::before {
    opacity: 1;
}

.view-more-btn {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(231, 76, 60, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.clickable-card:hover .view-more-btn {
    opacity: 1;
    transform: translateY(0);
}

.click-hint {
    color: #e74c3c;
    font-size: 0.85rem;
    font-weight: 600;
    text-align: center;
    margin-top: 10px;
    opacity: 0.8;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
}

.product-image {
    height: 200px;
    background: var(--light-gray);
    position: relative;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.halal-icon {
    position: absolute;
    top: 10px;
    right: 10px;
    background: var(--gold);
    color: var(--black);
    padding: 0.3rem 0.6rem;
    border-radius: 15px;
    font-size: 0.7rem;
    font-weight: 600;
}

.air-flown-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: #007bff;
    color: var(--white);
    padding: 0.3rem 0.6rem;
    border-radius: 15px;
    font-size: 0.7rem;
    font-weight: 600;
}

.product-info {
    padding: 1.5rem;
}

.product-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--black);
    margin-bottom: 0.5rem;
}

.product-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-red);
    margin-bottom: 1rem;
}

.product-origin {
    color: var(--primary-red);
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
}

.product-weight {
    color: var(--gray);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.bone-info {
    color: var(--gray);
    font-size: 0.8rem;
    font-style: italic;
    margin-bottom: 1rem;
    background: var(--light-gray);
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    display: inline-block;
}

/* Product Actions */
.product-actions {
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(0,0,0,0.1);
}

.discover-btn {
    background: linear-gradient(135deg, var(--primary-red) 0%, var(--dark-red) 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 0.95rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.discover-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
    background: linear-gradient(135deg, var(--dark-red) 0%, #c0392b 100%);
}

.discover-btn .arrow-icon {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.discover-btn:hover .arrow-icon {
    transform: translateX(3px);
}

/* Shop Locations Section */
.shop-locations {
    padding: 5rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-subtitle {
    color: var(--gray);
    font-size: 1.1rem;
    margin-top: 1rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.shops-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.shop-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.shop-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.shop-card.main-shop {
    border: 2px solid var(--primary-red);
    background: linear-gradient(135deg, #fff 0%, #fff5f5 100%);
}

.shop-card.main-shop::before {
    content: "★ MAIN";
    position: absolute;
    top: 15px;
    right: -25px;
    background: var(--primary-red);
    color: white;
    padding: 5px 30px;
    font-size: 0.8rem;
    font-weight: 600;
    transform: rotate(45deg);
}

.shop-number {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-red) 0%, #c0392b 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
}

.shop-info h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--black);
    margin-bottom: 0.8rem;
}

.shop-info p {
    color: var(--gray);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.shop-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.8rem;
}

.feature {
    background: var(--light-gray);
    color: var(--black);
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.4rem;
}

.feature i {
    color: var(--primary-red);
    font-size: 0.9rem;
}

.market-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.market-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(231, 76, 60, 0.1);
}

.market-card i {
    font-size: 2.5rem;
    color: var(--primary-red);
    margin-bottom: 1rem;
}

.market-card h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--black);
    margin-bottom: 0.8rem;
}

.market-card p {
    color: var(--gray);
    line-height: 1.6;
}

/* Reviews Carousel */
.reviews {
    padding: 5rem 0;
    background: linear-gradient(
        135deg,
        rgba(248, 249, 250, 0.9) 0%,
        rgba(230, 0, 0, 0.02) 50%,
        rgba(248, 249, 250, 0.8) 100%
    );
    backdrop-filter: blur(5px);
    overflow: hidden;
}

.reviews-carousel {
    margin-top: 3rem;
    overflow: hidden;
    position: relative;
}

.reviews-carousel::before,
.reviews-carousel::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 100px;
    z-index: 2;
    pointer-events: none;
}

.reviews-carousel::before {
    left: 0;
    background: linear-gradient(to right, rgba(248, 249, 250, 1), transparent);
}

.reviews-carousel::after {
    right: 0;
    background: linear-gradient(to left, rgba(248, 249, 250, 1), transparent);
}

.reviews-track {
    display: flex;
    gap: 2rem;
    animation: slideLeft 30s linear infinite;
    width: max-content;
}

.reviews-track:hover {
    animation-play-state: paused;
}

.reviews-carousel:hover::before,
.reviews-carousel:hover::after {
    opacity: 0.7;
}

.review-card {
    background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(255, 255, 255, 0.85) 100%
    );
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    min-width: 350px;
    max-width: 350px;
    border: 1px solid rgba(255, 255, 255, 0.5);
    flex-shrink: 0;
}

.review-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.stars {
    font-size: 1.5rem;
    color: #ffd700;
    margin-bottom: 1rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.review-card p {
    font-style: italic;
    color: var(--gray);
    margin-bottom: 1.5rem;
    line-height: 1.6;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
    font-size: 1rem;
}

.reviewer strong {
    color: var(--black);
    font-weight: 600;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
}

.reviewer span {
    color: var(--primary-red);
    font-size: 0.9rem;
    display: block;
    margin-top: 0.5rem;
    font-weight: 500;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
}

/* Location & Service Info */
.location-info {
    padding: 5rem 0;
    background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.8) 0%,
        rgba(230, 0, 0, 0.03) 50%,
        rgba(255, 255, 255, 0.7) 100%
    );
    backdrop-filter: blur(5px);
}

.location-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
    margin-top: 3rem;
}

.location-details {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.info-item {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    padding: 2rem;
    background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.9) 0%,
        rgba(255, 255, 255, 0.7) 100%
    );
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: 1px solid rgba(255, 255, 255, 0.5);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.info-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.info-item i {
    font-size: 2rem;
    color: var(--primary-red);
    margin-top: 0.5rem;
    flex-shrink: 0;
}

.info-content h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--black);
    margin-bottom: 0.5rem;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
}

.info-content p {
    color: var(--gray);
    margin-bottom: 0.5rem;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
}

.hours-note {
    font-size: 0.9rem;
    color: var(--gray);
    font-style: italic;
}

.delivery-status {
    color: var(--primary-red);
    font-weight: 600;
    font-size: 1.1rem;
}

.delivery-note {
    font-size: 0.9rem;
    color: var(--gray);
}

.map-link, .whatsapp-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-red);
    text-decoration: none;
    font-weight: 500;
    margin-top: 0.5rem;
    transition: color 0.3s ease;
}

.map-link:hover, .whatsapp-link:hover {
    color: var(--dark-red);
    text-decoration: underline;
}

.map-container {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.map-placeholder {
    position: relative;
    height: 400px;
    background: var(--light-gray);
}

.map-placeholder iframe {
    width: 100%;
    height: 100%;
}

.map-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(230, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.map-overlay p {
    color: var(--white);
    font-weight: 600;
    background: rgba(0, 0, 0, 0.7);
    padding: 1rem 2rem;
    border-radius: 25px;
    text-shadow: none;
}

.map-container:hover .map-overlay {
    opacity: 1;
}

/* Products Page Styles */
.page-header {
    background: linear-gradient(135deg, rgba(230, 0, 0, 0.7), rgba(204, 0, 0, 0.7));
    backdrop-filter: blur(5px);
    color: var(--white);
    padding: 120px 0 60px;
    text-align: center;
}

.page-header h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.page-header p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.breadcrumb {
    font-size: 1rem;
    opacity: 0.8;
}

.breadcrumb a {
    color: var(--white);
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.product-nav {
    background: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(5px);
    padding: 2rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 70px;
    z-index: 100;
}

.nav-menu a.active {
    color: var(--primary-red);
    font-weight: 600;
}

/* Product Categories Detail */
.product-categories-detail {
    padding: 5rem 0;
    background: var(--light-gray);
}

.category-section {
    margin-bottom: 4rem;
    background: var(--white);
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.category-section h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--black);
    margin-bottom: 1rem;
}

.category-intro {
    font-size: 1.1rem;
    color: var(--gray);
    margin-bottom: 2rem;
    line-height: 1.8;
}

.category-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature {
    text-align: center;
    padding: 1.5rem;
    background: var(--light-gray);
    border-radius: 15px;
}

.feature i {
    font-size: 2.5rem;
    color: var(--primary-red);
    margin-bottom: 1rem;
}

.feature h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--black);
    margin-bottom: 0.5rem;
}

.feature p {
    color: var(--gray);
    font-size: 0.9rem;
    line-height: 1.6;
}

/* Call to Action Section */
.cta-section {
    background: var(--primary-red);
    color: var(--white);
    padding: 4rem 0;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* Footer */
.footer {
    background: var(--black);
    color: var(--white);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: var(--white);
}

.footer-subtitle {
    font-size: 1rem;
    color: var(--primary-red);
    font-weight: 600;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.footer-section p {
    margin-bottom: 0.5rem;
    opacity: 0.8;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: var(--white);
    text-decoration: none;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.footer-section ul li a:hover {
    opacity: 1;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    color: var(--white);
    font-size: 1.5rem;
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: var(--primary-red);
}

.whatsapp-btn {
    background: #25d366;
    color: var(--white);
    padding: 1rem 2rem;
    border-radius: 25px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    transition: background 0.3s ease;
}

.whatsapp-btn:hover {
    background: #128c7e;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #333;
    opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }
    
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--white);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0,0,0,0.05);
        padding: 2rem 0;
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .hero {
        flex-direction: column;
        text-align: center;
        padding: 100px 1rem 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    /* Enhanced Hero Image Mobile Styles */
    .hero-image-container {
        padding: 1rem;
        margin-top: 2rem;
    }

    .hero-image-wrapper {
        transform: none;
        max-width: 100%;
    }

    .hero-image-wrapper:hover {
        transform: scale(1.02);
    }

    .floating-badge.halal-certified {
        top: 5%;
        right: -5%;
        padding: 0.8rem 1.2rem;
        font-size: 0.85rem;
    }

    .floating-badge.waterfront-market {
        bottom: 10%;
        left: -5%;
        padding: 0.8rem 1.2rem;
        font-size: 0.85rem;
    }

    .overlay-content {
        flex-direction: column;
        gap: 0.5rem;
        align-items: center;
    }

    .quality-badge,
    .freshness-indicator {
        padding: 0.6rem 1rem;
        font-size: 0.8rem;
    }
    
    /* Mobile About Section */
    .about-content-centered {
        padding: 0 1rem;
    }

    .about-title {
        font-size: 2.2rem;
    }

    .about-subtitle {
        font-size: 1.1rem;
        letter-spacing: 1px;
    }

    .about-intro {
        font-size: 1.1rem;
    }

    .trust-badges-centered {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
    }

    .trust-badge {
        padding: 1rem;
    }

    /* Mobile Shop Locations */
    .shops-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .shop-card {
        padding: 1.5rem;
    }

    .shop-number {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .shop-features {
        justify-content: center;
    }

    .feature {
        font-size: 0.8rem;
        padding: 0.3rem 0.6rem;
    }

    .market-info {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .market-card {
        padding: 1.5rem;
    }

    .market-card i {
        font-size: 2rem;
    }

    .trust-badge i {
        font-size: 1.5rem;
    }

    /* Mobile Location Section */
    .location-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .info-item {
        padding: 1.5rem;
        gap: 1rem;
    }

    .info-item i {
        font-size: 1.5rem;
    }

    .map-placeholder {
        height: 250px;
    }

    /* Mobile Reviews Carousel */
    .review-card {
        min-width: 280px;
        max-width: 280px;
        padding: 1.5rem;
    }

    .reviews-track {
        animation-duration: 25s;
    }

    .hero-buttons {
        justify-content: center;
    }
    
    .product-filters {
        justify-content: center;
    }
    
    .container {
        padding: 0 1rem;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    /* Enhanced Hero Image Small Mobile Styles */
    .hero-image-container {
        padding: 0.5rem;
    }

    .floating-badge {
        padding: 0.6rem 1rem;
        font-size: 0.8rem;
        border-radius: 15px;
    }

    .floating-badge.halal-certified {
        top: 2%;
        right: 0%;
    }

    .floating-badge.waterfront-market {
        bottom: 5%;
        left: 0%;
    }

    .image-frame {
        top: -10px;
        left: -10px;
        right: -10px;
        bottom: -10px;
        border-width: 2px;
    }

    .hero-image-wrapper:hover .image-frame {
        top: -12px;
        left: -12px;
        right: -12px;
        bottom: -12px;
    }

    .section-title {
        font-size: 2rem;
    }

    .hero-badges {
        justify-content: center;
    }

    .badge {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }

    /* Mobile background adjustments */
    body {
        background-attachment: scroll;
        background-position: center top;
    }

    body::before {
        background: rgba(255, 255, 255, 0.97);
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideLeft {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
}

/* Product Showcase Pages Styles */
.product-gallery {
    padding: 80px 0;
    background: #f8f9fa;
}

.gallery-header {
    text-align: center;
    margin-bottom: 60px;
}

.gallery-header h2 {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 15px;
}

.gallery-header p {
    font-size: 1.2rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

.products-showcase-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.product-showcase-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid #eee;
}

.product-showcase-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.product-image-container {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.product-image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-showcase-card:hover .product-image-container img {
    transform: scale(1.05);
}

.product-badges {
    position: absolute;
    top: 15px;
    left: 15px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.product-badges .badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.halal-badge {
    background: #27ae60;
    color: white;
}

.fresh-badge {
    background: #3498db;
    color: white;
}

.lean-badge {
    background: #e74c3c;
    color: white;
}

.juicy-badge {
    background: #f39c12;
    color: white;
}

.party-badge {
    background: #9b59b6;
    color: white;
}

.kids-badge {
    background: #ff6b6b;
    color: white;
}

.value-badge {
    background: #2ecc71;
    color: white;
}

.premium-badge {
    background: #8e44ad;
    color: white;
}

.exotic-badge {
    background: #d35400;
    color: white;
}

.quality-badge {
    background: #2980b9;
    color: white;
}

.cooking-badge {
    background: #e67e22;
    color: white;
}

.feast-badge {
    background: #c0392b;
    color: white;
}

.grill-badge {
    background: #e74c3c;
    color: white;
}

/* Additional Badge Styles */
.bone-badge {
    background: #8b4513;
    color: white;
}

.boneless-badge {
    background: #27ae60;
    color: white;
}

.convenient-badge {
    background: #3498db;
    color: white;
}

.bbq-badge {
    background: #d35400;
    color: white;
}

.tender-badge {
    background: #e67e22;
    color: white;
}

.specialty-badge {
    background: #9b59b6;
    color: white;
}

.flavor-badge {
    background: #c0392b;
    color: white;
}

.versatile-badge {
    background: #16a085;
    color: white;
}

.family-badge {
    background: #e91e63;
    color: white;
}

.traditional-badge {
    background: #795548;
    color: white;
}

.favorite-badge {
    background: #ff9800;
    color: white;
}

.comfort-badge {
    background: #607d8b;
    color: white;
}

.nutritious-badge {
    background: #4caf50;
    color: white;
}

.delicacy-badge {
    background: #673ab7;
    color: white;
}

.protein-badge {
    background: #f44336;
    color: white;
}

.gourmet-badge {
    background: #ff5722;
    color: white;
}

.delicate-badge {
    background: #ffc107;
    color: black;
}

.variety-badge {
    background: #009688;
    color: white;
}

.product-info {
    padding: 25px;
}

.product-info h3 {
    font-size: 1.4rem;
    color: #2c3e50;
    margin-bottom: 8px;
    font-weight: 600;
}

.product-weight {
    color: #e74c3c;
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 12px;
}

.product-description {
    color: #666;
    line-height: 1.6;
    margin-bottom: 15px;
}

.product-features {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.product-features span {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.85rem;
    color: #27ae60;
    font-weight: 500;
}

.product-features i {
    font-size: 0.8rem;
}

.quality-section {
    padding: 80px 0;
    background: white;
}

.quality-section h2 {
    text-align: center;
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 50px;
}

.quality-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.quality-item {
    text-align: center;
    padding: 30px 20px;
    border-radius: 15px;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.quality-item:hover {
    background: #e74c3c;
    color: white;
    transform: translateY(-5px);
}

.quality-item i {
    font-size: 3rem;
    color: #e74c3c;
    margin-bottom: 20px;
    transition: color 0.3s ease;
}

.quality-item:hover i {
    color: white;
}

.quality-item h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    color: #2c3e50;
    transition: color 0.3s ease;
}

.quality-item:hover h3 {
    color: white;
}

.quality-item p {
    color: #666;
    line-height: 1.6;
    transition: color 0.3s ease;
}

.quality-item:hover p {
    color: white;
}

.cta-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: 15px;
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.cta-buttons .btn {
    padding: 15px 30px;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.cta-buttons .btn-primary {
    background: white;
    color: #e74c3c;
}

.cta-buttons .btn-primary:hover {
    background: #f8f9fa;
    transform: translateY(-2px);
}

.cta-buttons .btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.cta-buttons .btn-secondary:hover {
    background: white;
    color: #e74c3c;
}

/* Responsive Design for Product Showcase */
@media (max-width: 768px) {
    .products-showcase-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .gallery-header h2 {
        font-size: 2rem;
    }

    .quality-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .cta-buttons .btn {
        width: 250px;
        justify-content: center;
    }
}

/* ===== PRODUCT GALLERY MODAL STYLES ===== */

/* Click Overlay for Product Cards */
.clickable-product {
    cursor: pointer;
    position: relative;
}

.click-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 20px;
    z-index: 10;
}

.clickable-product:hover .click-overlay {
    opacity: 1;
}

.click-icon {
    color: white;
    text-align: center;
    font-size: 1.1rem;
    font-weight: 600;
}

.click-icon i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: block;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 2% auto;
    padding: 0;
    border-radius: 20px;
    width: 90%;
    max-width: 1000px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-red) 0%, #c0392b 100%);
    color: white;
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.close {
    color: white;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    transition: opacity 0.3s ease;
    line-height: 1;
}

.close:hover {
    opacity: 0.7;
}

.modal-body {
    padding: 2rem;
    position: relative;
}

.gallery-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.main-image-container {
    position: relative;
    text-align: center;
    background: #f8f9fa;
    border-radius: 15px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 300px;
}

.main-image-container img {
    width: 100%;
    max-height: 400px;
    object-fit: contain;
    border-radius: 15px;
    background: #f8f9fa;
}

.image-counter {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.thumbnail-container {
    max-height: 120px;
    overflow-y: auto;
}

.thumbnail-grid {
    display: flex;
    gap: 1rem;
    padding: 0.5rem 0;
    justify-content: center;
    flex-wrap: wrap;
}

.thumbnail {
    width: 80px;
    height: 80px;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 3px solid transparent;
    object-fit: contain;
    background: #f8f9fa;
}

.thumbnail:hover {
    transform: scale(1.1);
    border-color: var(--primary-red);
}

.thumbnail.active {
    border-color: var(--primary-red);
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
}

.gallery-navigation {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    padding: 0 1rem;
    pointer-events: none;
}

.nav-btn {
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    pointer-events: all;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-btn:hover {
    background: var(--primary-red);
    transform: scale(1.1);
}

.nav-btn:disabled {
    opacity: 0.3;
    cursor: not-allowed;
}

.nav-btn:disabled:hover {
    background: rgba(0, 0, 0, 0.7);
    transform: none;
}

/* Mobile Responsive for Modal */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 5% auto;
        max-height: 85vh;
    }

    .modal-header {
        padding: 1rem 1.5rem;
    }

    .modal-header h3 {
        font-size: 1.2rem;
    }

    .modal-body {
        padding: 1rem;
    }

    .main-image-container img {
        max-height: 250px;
        object-fit: contain;
        background: #f8f9fa;
    }

    .thumbnail {
        width: 60px;
        height: 60px;
        object-fit: contain;
        background: #f8f9fa;
    }

    .nav-btn {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .gallery-navigation {
        padding: 0 0.5rem;
    }
}

@media (max-width: 480px) {
    .modal-content {
        width: 98%;
        margin: 2% auto;
    }

    .modal-header {
        padding: 0.8rem 1rem;
    }

    .modal-body {
        padding: 0.8rem;
    }

    .main-image-container img {
        max-height: 200px;
        object-fit: contain;
        background: #f8f9fa;
    }

    .thumbnail {
        width: 50px;
        height: 50px;
        object-fit: contain;
        background: #f8f9fa;
    }

    .thumbnail-grid {
        gap: 0.5rem;
    }

    .click-icon {
        font-size: 0.9rem;
    }

    .click-icon i {
        font-size: 1.5rem;
    }
}