<!DOCTYPE html>
<html>
<head>
    <title>Image Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: #f0f0f0;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        img {
            max-width: 100%;
            border: 2px solid #ddd;
            border-radius: 5px;
        }
        .bg-test {
            width: 100%;
            height: 300px;
            border: 2px solid #e60000;
            margin: 20px 0;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        }
    </style>
</head>
<body>
    <h1>🖼️ Image Accessibility Test</h1>
    
    <div class="test-container">
        <h2>Test 1: Direct Image Tag</h2>
        <img src="assets/images/shop.jpg" alt="Shop Image" 
             onload="document.getElementById('img-result').innerHTML='✅ SUCCESS: Image loaded via &lt;img&gt; tag'" 
             onerror="document.getElementById('img-result').innerHTML='❌ FAILED: Image not found via &lt;img&gt; tag'">
        <p id="img-result">Loading...</p>
    </div>
    
    <div class="test-container">
        <h2>Test 2: CSS Background (Relative Path)</h2>
        <div class="bg-test" style="background-image: url('assets/images/shop.jpg');">
            Background Test Area
        </div>
        <p>If you see the shop image as background above, the relative path works!</p>
    </div>
    
    <div class="test-container">
        <h2>Test 3: CSS Background (Absolute Path)</h2>
        <div class="bg-test" style="background-image: url('/assets/images/shop.jpg');">
            Background Test Area
        </div>
        <p>If you see the shop image as background above, the absolute path works!</p>
    </div>
    
    <div class="test-container">
        <h2>Test 4: File Information</h2>
        <p><strong>Expected file location:</strong> assets/images/shop.jpg</p>
        <p><strong>Direct URL test:</strong> <a href="assets/images/shop.jpg" target="_blank">Click to open image directly</a></p>
        <p><strong>Server URL:</strong> <a href="http://localhost:8000/assets/images/shop.jpg" target="_blank">Full server path</a></p>
    </div>
    
    <div class="test-container">
        <h2>Test 5: JavaScript Detection</h2>
        <p id="js-result">Checking...</p>
    </div>
    
    <script>
        // Test if image exists via JavaScript
        const img = new Image();
        img.onload = function() {
            document.getElementById('js-result').innerHTML = '✅ SUCCESS: Image accessible via JavaScript<br>Dimensions: ' + this.width + 'x' + this.height + 'px';
        };
        img.onerror = function() {
            document.getElementById('js-result').innerHTML = '❌ FAILED: Image not accessible via JavaScript';
        };
        img.src = 'assets/images/shop.jpg';
    </script>
</body>
</html>
